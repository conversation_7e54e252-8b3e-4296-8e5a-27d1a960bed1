# Whale Detection and Analysis Configuration

# Core whale detection settings
whale_detection:
  enabled: true
  min_whale_amount_sol: 100.0        # Minimum SOL amount to consider as whale transaction
  min_whale_amount_usd: 18000.0      # Minimum USD value to consider as whale transaction
  monitoring_interval: 10            # Seconds between monitoring cycles
  max_transactions_per_cycle: 1000   # Maximum transactions to process per cycle
  confidence_threshold: 0.6          # Minimum confidence score for whale signals

# Whale wallet tracking
whale_tracking:
  enabled: true
  track_known_whales: true
  track_exchange_flows: true
  track_validator_stakes: true
  update_interval: 300               # Seconds between whale wallet updates
  max_wallets_to_track: 500         # Maximum number of whale wallets to track

# Flow analysis settings
flow_analysis:
  enabled: true
  accumulation_threshold_sol: 1000   # SOL threshold for accumulation detection
  distribution_threshold_sol: 500    # SOL threshold for distribution detection
  time_window_minutes: 60           # Time window for flow analysis
  exchange_flow_weight: 1.5         # Weight multiplier for exchange flows
  whale_flow_weight: 2.0            # Weight multiplier for known whale flows

# Exchange monitoring
exchange_monitoring:
  enabled: true
  track_inflows: true               # Track SOL flowing into exchanges (bearish)
  track_outflows: true              # Track SOL flowing out of exchanges (bullish)
  inflow_threshold_sol: 500         # Minimum inflow to trigger signal
  outflow_threshold_sol: 500        # Minimum outflow to trigger signal
  exchange_impact_multiplier: 1.2   # Impact multiplier for exchange flows

# Smart money tracking
smart_money:
  enabled: true
  copy_threshold_sol: 200           # Minimum transaction size to consider copying
  copy_confidence_threshold: 0.7    # Minimum confidence to copy whale trades
  max_copy_percentage: 0.3          # Maximum percentage of capital to use for copying
  copy_delay_seconds: 30            # Delay before copying whale trades
  stop_loss_percentage: 0.05        # Stop loss for copied trades

# Signal generation
signal_generation:
  enabled: true
  accumulation_signal_strength: 0.8  # Signal strength for accumulation patterns
  distribution_signal_strength: 0.6  # Signal strength for distribution patterns
  whale_confirmation_bonus: 0.2      # Bonus confidence when whales confirm signals
  exchange_flow_signal_strength: 0.7 # Signal strength for exchange flows
  
  # Signal timeframes
  short_term_minutes: 15            # Short-term signal timeframe
  medium_term_minutes: 60           # Medium-term signal timeframe
  long_term_minutes: 240            # Long-term signal timeframe

# Data storage and retention
data_storage:
  enabled: true
  save_whale_transactions: true
  save_flow_analysis: true
  save_signals: true
  retention_days: 30                # Days to retain whale data
  backup_enabled: true
  backup_interval_hours: 6          # Hours between data backups

# Performance and limits
performance:
  max_concurrent_requests: 10       # Maximum concurrent RPC requests
  request_timeout_seconds: 30       # Timeout for RPC requests
  rate_limit_requests_per_second: 5 # Rate limit for API calls
  memory_limit_mb: 512              # Memory limit for whale data
  
# Notification settings
notifications:
  enabled: true
  whale_detection_alerts: true      # Alert on whale transaction detection
  flow_analysis_alerts: true        # Alert on significant flow changes
  smart_money_alerts: true          # Alert on smart money opportunities
  exchange_flow_alerts: true        # Alert on significant exchange flows
  
  # Alert thresholds
  major_whale_threshold_sol: 1000   # SOL threshold for major whale alerts
  critical_flow_threshold_sol: 5000 # SOL threshold for critical flow alerts

# Integration settings
integration:
  rl_learning_enabled: true         # Enable RL learning from whale data
  strategy_integration_enabled: true # Integrate whale signals with trading strategies
  risk_management_integration: true  # Use whale data for risk management
  position_sizing_integration: true  # Use whale data for position sizing

# Whale wallet categories
whale_categories:
  validators:
    weight: 1.0                     # Weight for validator transactions
    min_stake_sol: 10000           # Minimum stake to consider as validator whale
  
  institutions:
    weight: 1.5                     # Weight for institutional transactions
    min_transaction_sol: 500        # Minimum transaction for institutional whale
  
  exchanges:
    weight: 1.2                     # Weight for exchange transactions
    flow_impact_multiplier: 1.3     # Impact multiplier for exchange flows
  
  defi_protocols:
    weight: 1.1                     # Weight for DeFi protocol transactions
    min_tvl_impact_sol: 1000       # Minimum TVL impact to consider significant

# Market impact estimation
market_impact:
  enabled: true
  small_transaction_impact: 0.001   # Market impact for small whale transactions (0.1%)
  medium_transaction_impact: 0.005  # Market impact for medium whale transactions (0.5%)
  large_transaction_impact: 0.02    # Market impact for large whale transactions (2%)
  
  # Transaction size categories (SOL)
  small_whale_threshold: 200
  medium_whale_threshold: 1000
  large_whale_threshold: 5000

# Advanced features
advanced:
  pattern_recognition_enabled: true  # Enable whale pattern recognition
  predictive_analysis_enabled: true  # Enable predictive whale analysis
  cross_chain_tracking_enabled: false # Enable cross-chain whale tracking (future)
  ml_enhancement_enabled: true       # Enable ML enhancement of whale detection
