#!/usr/bin/env python3
"""
Adaptive Strategy Manager - RL-Enhanced Learning System
Enables the trading system to learn and adapt over time for improved profitability.
"""

import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
from collections import deque
import pickle

logger = logging.getLogger(__name__)

class AdaptiveStrategyManager:
    """Manages strategy adaptation and learning based on performance feedback."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize adaptive strategy manager."""
        self.config = config
        self.learning_config = config.get('adaptive_learning', {})
        
        # Learning parameters
        self.learning_rate = self.learning_config.get('learning_rate', 0.01)
        self.memory_size = self.learning_config.get('memory_size', 1000)
        self.adaptation_threshold = self.learning_config.get('adaptation_threshold', 0.1)
        self.min_samples_for_learning = self.learning_config.get('min_samples', 50)
        
        # Strategy performance tracking
        self.strategy_performance = {}
        self.trade_history = deque(maxlen=self.memory_size)
        self.performance_window = deque(maxlen=100)  # Last 100 trades
        
        # Strategy weights (start with equal weights)
        self.strategy_weights = {
            'momentum': 0.33,
            'mean_reversion': 0.33,
            'breakout': 0.34
        }
        
        # Confidence thresholds (adaptive)
        self.confidence_thresholds = {
            'momentum': 0.6,
            'mean_reversion': 0.7,
            'breakout': 0.8
        }
        
        # Position sizing multipliers (adaptive)
        self.position_multipliers = {
            'momentum': 1.0,
            'mean_reversion': 1.0,
            'breakout': 1.0
        }
        
        # Learning state
        self.total_trades = 0
        self.profitable_trades = 0
        self.total_pnl = 0.0
        self.last_adaptation = datetime.now()
        
        # Load previous learning state if exists
        self._load_learning_state()
        
        logger.info(f"Adaptive Strategy Manager initialized with learning rate {self.learning_rate}")
    
    def record_trade_result(self, signal: Dict[str, Any], result: Dict[str, Any], pnl: float):
        """Record a trade result for learning."""
        
        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'strategy': signal.get('strategy', 'unknown'),
            'action': signal.get('action'),
            'confidence': signal.get('confidence', 0.0),
            'size': signal.get('size', 0.0),
            'price': signal.get('price', 0.0),
            'success': result.get('success', False),
            'pnl': pnl,
            'execution_time': result.get('execution_time', 0.0),
            'market_regime': signal.get('metadata', {}).get('market_regime', 'unknown')
        }
        
        # Add to history
        self.trade_history.append(trade_record)
        self.performance_window.append(pnl)
        
        # Update counters
        self.total_trades += 1
        if pnl > 0:
            self.profitable_trades += 1
        self.total_pnl += pnl
        
        # Update strategy performance
        strategy = trade_record['strategy']
        if strategy not in self.strategy_performance:
            self.strategy_performance[strategy] = {
                'trades': 0,
                'profitable': 0,
                'total_pnl': 0.0,
                'avg_pnl': 0.0,
                'win_rate': 0.0,
                'confidence_sum': 0.0,
                'avg_confidence': 0.0
            }
        
        perf = self.strategy_performance[strategy]
        perf['trades'] += 1
        if pnl > 0:
            perf['profitable'] += 1
        perf['total_pnl'] += pnl
        perf['avg_pnl'] = perf['total_pnl'] / perf['trades']
        perf['win_rate'] = perf['profitable'] / perf['trades']
        perf['confidence_sum'] += trade_record['confidence']
        perf['avg_confidence'] = perf['confidence_sum'] / perf['trades']
        
        logger.info(f"Recorded trade: {strategy} {trade_record['action']} PnL: {pnl:.6f}")
        
        # Check if adaptation is needed
        if self._should_adapt():
            self._adapt_strategies()
    
    def _should_adapt(self) -> bool:
        """Determine if strategy adaptation should occur."""
        
        # Need minimum samples
        if len(self.trade_history) < self.min_samples_for_learning:
            return False
        
        # Check time since last adaptation
        time_since_adaptation = datetime.now() - self.last_adaptation
        if time_since_adaptation < timedelta(hours=1):  # Adapt at most once per hour
            return False
        
        # Check performance degradation
        if len(self.performance_window) >= 20:
            recent_performance = np.mean(list(self.performance_window)[-20:])
            overall_performance = np.mean(list(self.performance_window))
            
            if recent_performance < overall_performance - self.adaptation_threshold:
                logger.info("Performance degradation detected, triggering adaptation")
                return True
        
        # Periodic adaptation (every 100 trades)
        if self.total_trades % 100 == 0:
            logger.info("Periodic adaptation triggered")
            return True
        
        return False
    
    def _adapt_strategies(self):
        """Adapt strategy weights and parameters based on performance."""
        
        logger.info("🧠 Starting strategy adaptation...")
        
        # Calculate strategy performance scores
        strategy_scores = {}
        total_score = 0.0
        
        for strategy, perf in self.strategy_performance.items():
            if perf['trades'] >= 10:  # Need minimum trades for reliable stats
                # Score based on win rate, average PnL, and confidence
                score = (
                    perf['win_rate'] * 0.4 +  # 40% weight on win rate
                    max(0, perf['avg_pnl']) * 1000 * 0.4 +  # 40% weight on avg PnL
                    perf['avg_confidence'] * 0.2  # 20% weight on confidence
                )
                strategy_scores[strategy] = max(0.1, score)  # Minimum score of 0.1
                total_score += strategy_scores[strategy]
        
        if total_score > 0:
            # Update strategy weights based on performance
            old_weights = self.strategy_weights.copy()
            
            for strategy in self.strategy_weights:
                if strategy in strategy_scores:
                    self.strategy_weights[strategy] = strategy_scores[strategy] / total_score
                else:
                    self.strategy_weights[strategy] = 0.1  # Default weight for untested strategies
            
            # Normalize weights
            total_weight = sum(self.strategy_weights.values())
            for strategy in self.strategy_weights:
                self.strategy_weights[strategy] /= total_weight
            
            # Adapt confidence thresholds based on performance
            for strategy, perf in self.strategy_performance.items():
                if perf['trades'] >= 10:
                    if perf['win_rate'] > 0.6:
                        # Lower threshold for good performing strategies
                        self.confidence_thresholds[strategy] = max(0.5, 
                            self.confidence_thresholds[strategy] - self.learning_rate)
                    elif perf['win_rate'] < 0.4:
                        # Raise threshold for poor performing strategies
                        self.confidence_thresholds[strategy] = min(0.9, 
                            self.confidence_thresholds[strategy] + self.learning_rate)
            
            # Adapt position multipliers
            for strategy, perf in self.strategy_performance.items():
                if perf['trades'] >= 10:
                    if perf['avg_pnl'] > 0 and perf['win_rate'] > 0.5:
                        # Increase position size for profitable strategies
                        self.position_multipliers[strategy] = min(1.5, 
                            self.position_multipliers[strategy] + self.learning_rate)
                    elif perf['avg_pnl'] < 0:
                        # Decrease position size for losing strategies
                        self.position_multipliers[strategy] = max(0.5, 
                            self.position_multipliers[strategy] - self.learning_rate)
            
            self.last_adaptation = datetime.now()
            
            # Log adaptation results
            logger.info("🎯 Strategy adaptation completed:")
            logger.info(f"   Old weights: {old_weights}")
            logger.info(f"   New weights: {self.strategy_weights}")
            logger.info(f"   Confidence thresholds: {self.confidence_thresholds}")
            logger.info(f"   Position multipliers: {self.position_multipliers}")
            
            # Save learning state
            self._save_learning_state()
    
    def get_adapted_signal(self, base_signal: Dict[str, Any]) -> Dict[str, Any]:
        """Apply learned adaptations to a trading signal."""
        
        strategy = base_signal.get('strategy', 'unknown')
        
        # Apply confidence threshold
        confidence_threshold = self.confidence_thresholds.get(strategy, 0.7)
        if base_signal.get('confidence', 0) < confidence_threshold:
            # Signal doesn't meet adapted threshold
            adapted_signal = base_signal.copy()
            adapted_signal['action'] = 'HOLD'
            adapted_signal['adaptation_reason'] = f'Below confidence threshold {confidence_threshold:.2f}'
            return adapted_signal
        
        # Apply position size multiplier
        adapted_signal = base_signal.copy()
        position_multiplier = self.position_multipliers.get(strategy, 1.0)
        
        if 'size' in adapted_signal:
            adapted_signal['size'] *= position_multiplier
            adapted_signal['position_multiplier'] = position_multiplier
        
        # Add adaptation metadata
        adapted_signal['adapted'] = True
        adapted_signal['strategy_weight'] = self.strategy_weights.get(strategy, 0.33)
        adapted_signal['confidence_threshold'] = confidence_threshold
        
        return adapted_signal
    
    def get_learning_metrics(self) -> Dict[str, Any]:
        """Get current learning and adaptation metrics."""
        
        win_rate = self.profitable_trades / self.total_trades if self.total_trades > 0 else 0
        avg_pnl = self.total_pnl / self.total_trades if self.total_trades > 0 else 0
        
        recent_performance = 0
        if len(self.performance_window) >= 10:
            recent_performance = np.mean(list(self.performance_window)[-10:])
        
        return {
            'total_trades': self.total_trades,
            'win_rate': win_rate,
            'avg_pnl': avg_pnl,
            'total_pnl': self.total_pnl,
            'recent_performance': recent_performance,
            'strategy_weights': self.strategy_weights.copy(),
            'confidence_thresholds': self.confidence_thresholds.copy(),
            'position_multipliers': self.position_multipliers.copy(),
            'strategy_performance': self.strategy_performance.copy(),
            'last_adaptation': self.last_adaptation.isoformat(),
            'learning_enabled': True
        }
    
    def _save_learning_state(self):
        """Save learning state to disk."""
        try:
            state = {
                'strategy_weights': self.strategy_weights,
                'confidence_thresholds': self.confidence_thresholds,
                'position_multipliers': self.position_multipliers,
                'strategy_performance': self.strategy_performance,
                'total_trades': self.total_trades,
                'profitable_trades': self.profitable_trades,
                'total_pnl': self.total_pnl,
                'last_adaptation': self.last_adaptation.isoformat()
            }
            
            os.makedirs('output/learning', exist_ok=True)
            with open('output/learning/adaptive_state.json', 'w') as f:
                json.dump(state, f, indent=2)
                
            logger.debug("Learning state saved successfully")
            
        except Exception as e:
            logger.error(f"Error saving learning state: {e}")
    
    def _load_learning_state(self):
        """Load learning state from disk."""
        try:
            if os.path.exists('output/learning/adaptive_state.json'):
                with open('output/learning/adaptive_state.json', 'r') as f:
                    state = json.load(f)
                
                self.strategy_weights = state.get('strategy_weights', self.strategy_weights)
                self.confidence_thresholds = state.get('confidence_thresholds', self.confidence_thresholds)
                self.position_multipliers = state.get('position_multipliers', self.position_multipliers)
                self.strategy_performance = state.get('strategy_performance', {})
                self.total_trades = state.get('total_trades', 0)
                self.profitable_trades = state.get('profitable_trades', 0)
                self.total_pnl = state.get('total_pnl', 0.0)
                
                if 'last_adaptation' in state:
                    self.last_adaptation = datetime.fromisoformat(state['last_adaptation'])
                
                logger.info("Learning state loaded successfully")
                logger.info(f"Loaded {self.total_trades} trades, {self.profitable_trades} profitable")
                
        except Exception as e:
            logger.warning(f"Could not load learning state: {e}")
            logger.info("Starting with fresh learning state")
