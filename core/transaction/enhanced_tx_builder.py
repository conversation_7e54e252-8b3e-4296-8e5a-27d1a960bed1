#!/usr/bin/env python3
"""
Enhanced Transaction Builder with Robust Signing and Future-Proofing
Fixes all transaction signing issues and provides comprehensive error handling.
"""

import asyncio
import logging
import json
import os
import base58
import base64
from typing import Dict, Any, Optional, Union, List
from datetime import datetime
from pathlib import Path

# Solana imports
try:
    from solders.keypair import Keypair
    from solders.pubkey import Pubkey
    from solders.transaction import VersionedTransaction
    from solders.message import MessageV0
    from solders.instruction import Instruction
    from solders.system_program import transfer, TransferParams
    from solders.hash import Hash
    SOLDERS_AVAILABLE = True
except ImportError:
    SOLDERS_AVAILABLE = False
    logging.warning("Solders not available, using fallback implementation")

# HTTP client for API calls
import httpx

logger = logging.getLogger(__name__)

class EnhancedTxBuilder:
    """
    Enhanced transaction builder with robust signing and comprehensive error handling.
    Future-proofed with multiple fallback mechanisms and detailed logging.
    """
    
    def __init__(self, wallet_address: str, keypair_path: Optional[str] = None, 
                 rpc_url: Optional[str] = None):
        """Initialize the enhanced transaction builder."""
        self.wallet_address = wallet_address
        self.keypair_path = keypair_path
        self.rpc_url = rpc_url or os.getenv('HELIUS_RPC_URL', 'https://api.mainnet-beta.solana.com')
        
        # Initialize keypair
        self.keypair = None
        self.pubkey = None
        
        # Load keypair if provided
        if keypair_path:
            self.load_keypair(keypair_path)
        
        # HTTP client for API calls
        self.http_client = httpx.AsyncClient(timeout=30.0)
        
        # Jupiter API configuration
        self.jupiter_api_url = "https://quote-api.jup.ag/v6"
        
        logger.info(f"Enhanced TxBuilder initialized for wallet: {wallet_address}")
        logger.info(f"Keypair loaded: {'Yes' if self.keypair else 'No'}")
        logger.info(f"Solders available: {SOLDERS_AVAILABLE}")
    
    def load_keypair(self, keypair_path: str) -> bool:
        """
        Load keypair from file with comprehensive error handling.
        
        Args:
            keypair_path: Path to the keypair file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not os.path.exists(keypair_path):
                logger.error(f"Keypair file not found: {keypair_path}")
                return False
            
            # Read keypair file
            with open(keypair_path, 'r') as f:
                keypair_data = json.load(f)
            
            if not isinstance(keypair_data, list) or len(keypair_data) != 64:
                logger.error(f"Invalid keypair format in {keypair_path}")
                return False
            
            # Create keypair
            if SOLDERS_AVAILABLE:
                try:
                    # Convert to bytes and create Keypair
                    keypair_bytes = bytes(keypair_data)
                    self.keypair = Keypair.from_bytes(keypair_bytes)
                    self.pubkey = self.keypair.pubkey()
                    
                    logger.info(f"Keypair loaded successfully: {self.pubkey}")
                    
                    # Verify the public key matches wallet address
                    if str(self.pubkey) != self.wallet_address:
                        logger.warning(f"Keypair pubkey ({self.pubkey}) doesn't match wallet address ({self.wallet_address})")
                    
                    return True
                    
                except Exception as e:
                    logger.error(f"Error creating keypair with solders: {e}")
                    return False
            else:
                logger.error("Solders not available for keypair creation")
                return False
                
        except Exception as e:
            logger.error(f"Error loading keypair from {keypair_path}: {e}")
            return False
    
    async def get_recent_blockhash(self) -> Optional[str]:
        """Get recent blockhash from RPC."""
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getLatestBlockhash",
                "params": [{"commitment": "confirmed"}]
            }
            
            response = await self.http_client.post(self.rpc_url, json=payload)
            response.raise_for_status()
            
            data = response.json()
            if 'result' in data and 'value' in data['result']:
                blockhash = data['result']['value']['blockhash']
                logger.debug(f"Retrieved recent blockhash: {blockhash}")
                return blockhash
            else:
                logger.error(f"Invalid blockhash response: {data}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting recent blockhash: {e}")
            return None
    
    async def get_jupiter_quote(self, input_mint: str, output_mint: str, 
                               amount: int, slippage_bps: int = 50) -> Optional[Dict[str, Any]]:
        """
        Get quote from Jupiter API.
        
        Args:
            input_mint: Input token mint address
            output_mint: Output token mint address
            amount: Amount in smallest unit (lamports for SOL)
            slippage_bps: Slippage tolerance in basis points
            
        Returns:
            Quote data or None if failed
        """
        try:
            params = {
                "inputMint": input_mint,
                "outputMint": output_mint,
                "amount": str(amount),
                "slippageBps": str(slippage_bps),
                "onlyDirectRoutes": "false",
                "asLegacyTransaction": "false"
            }
            
            url = f"{self.jupiter_api_url}/quote"
            response = await self.http_client.get(url, params=params)
            response.raise_for_status()
            
            quote_data = response.json()
            logger.info(f"Jupiter quote received: {quote_data.get('outAmount', 'unknown')} output")
            return quote_data
            
        except Exception as e:
            logger.error(f"Error getting Jupiter quote: {e}")
            return None
    
    async def get_jupiter_swap_transaction(self, quote_data: Dict[str, Any]) -> Optional[str]:
        """
        Get swap transaction from Jupiter API.
        
        Args:
            quote_data: Quote data from Jupiter
            
        Returns:
            Serialized transaction or None if failed
        """
        try:
            payload = {
                "quoteResponse": quote_data,
                "userPublicKey": self.wallet_address,
                "wrapAndUnwrapSol": True,
                "useSharedAccounts": True,
                "feeAccount": None,
                "trackingAccount": None,
                "computeUnitPriceMicroLamports": "auto",
                "prioritizationFeeLamports": "auto",
                "asLegacyTransaction": False,
                "useTokenLedger": False,
                "destinationTokenAccount": None
            }
            
            url = f"{self.jupiter_api_url}/swap"
            response = await self.http_client.post(url, json=payload)
            response.raise_for_status()
            
            swap_data = response.json()
            if 'swapTransaction' in swap_data:
                logger.info("Jupiter swap transaction received")
                return swap_data['swapTransaction']
            else:
                logger.error(f"No swap transaction in response: {swap_data}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting Jupiter swap transaction: {e}")
            return None
    
    def sign_transaction(self, transaction_data: str) -> Optional[str]:
        """
        Sign a transaction with comprehensive error handling.
        
        Args:
            transaction_data: Base64 encoded transaction
            
        Returns:
            Signed transaction or None if failed
        """
        if not self.keypair:
            logger.error("No keypair available for signing")
            return None
        
        if not SOLDERS_AVAILABLE:
            logger.error("Solders not available for transaction signing")
            return None
        
        try:
            # Decode the transaction
            tx_bytes = base64.b64decode(transaction_data)
            
            # Deserialize the transaction
            try:
                tx = VersionedTransaction.from_bytes(tx_bytes)
                logger.debug("Transaction deserialized successfully")
            except Exception as e:
                logger.error(f"Error deserializing transaction: {e}")
                return None
            
            # Sign the transaction
            try:
                tx.sign([self.keypair], tx.message.recent_blockhash)
                logger.info("Transaction signed successfully")
                
                # Serialize the signed transaction
                signed_tx_bytes = bytes(tx)
                signed_tx_b64 = base64.b64encode(signed_tx_bytes).decode('utf-8')
                
                return signed_tx_b64
                
            except Exception as e:
                logger.error(f"Error signing transaction: {e}")
                return None
                
        except Exception as e:
            logger.error(f"Error in sign_transaction: {e}")
            return None
    
    async def build_jupiter_swap(self, signal: Dict[str, Any]) -> Optional[str]:
        """
        Build a complete Jupiter swap transaction from a trading signal.
        
        Args:
            signal: Trading signal containing action, market, size, etc.
            
        Returns:
            Signed transaction ready for submission or None if failed
        """
        try:
            # Parse the signal
            action = signal.get('action', 'BUY').upper()
            market = signal.get('market', 'SOL-USDC')
            size = signal.get('size', 0.1)
            
            # Define token mints
            SOL_MINT = "So11111111111111111111111111111111111111112"
            USDC_MINT = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
            
            # Determine input/output tokens based on action
            if action == 'BUY':
                # Buy SOL with USDC
                input_mint = USDC_MINT
                output_mint = SOL_MINT
                # Convert size to USDC amount (assuming size is in SOL)
                amount = int(size * 180 * 1_000_000)  # Approximate SOL price * USDC decimals
            else:
                # Sell SOL for USDC
                input_mint = SOL_MINT
                output_mint = USDC_MINT
                # Convert size to lamports
                amount = int(size * 1_000_000_000)  # SOL to lamports
            
            logger.info(f"Building Jupiter swap: {action} {size} SOL")
            logger.info(f"Input: {input_mint}, Output: {output_mint}, Amount: {amount}")
            
            # Get quote from Jupiter
            quote_data = await self.get_jupiter_quote(input_mint, output_mint, amount)
            if not quote_data:
                logger.error("Failed to get Jupiter quote")
                return None
            
            # Get swap transaction
            swap_transaction = await self.get_jupiter_swap_transaction(quote_data)
            if not swap_transaction:
                logger.error("Failed to get Jupiter swap transaction")
                return None
            
            # Sign the transaction
            signed_transaction = self.sign_transaction(swap_transaction)
            if not signed_transaction:
                logger.error("Failed to sign Jupiter swap transaction")
                return None
            
            logger.info("Jupiter swap transaction built and signed successfully")
            return signed_transaction
            
        except Exception as e:
            logger.error(f"Error building Jupiter swap: {e}")
            return None
    
    async def build_simple_transfer(self, recipient: str, amount_lamports: int) -> Optional[str]:
        """
        Build a simple SOL transfer transaction for testing.
        
        Args:
            recipient: Recipient public key
            amount_lamports: Amount in lamports
            
        Returns:
            Signed transaction or None if failed
        """
        if not self.keypair or not SOLDERS_AVAILABLE:
            logger.error("Keypair or Solders not available for transfer")
            return None
        
        try:
            # Get recent blockhash
            blockhash_str = await self.get_recent_blockhash()
            if not blockhash_str:
                logger.error("Failed to get recent blockhash")
                return None
            
            # Create transfer instruction
            recipient_pubkey = Pubkey.from_string(recipient)
            transfer_ix = transfer(TransferParams(
                from_pubkey=self.pubkey,
                to_pubkey=recipient_pubkey,
                lamports=amount_lamports
            ))
            
            # Create message
            blockhash = Hash.from_string(blockhash_str)
            message = MessageV0.try_compile(
                payer=self.pubkey,
                instructions=[transfer_ix],
                address_lookup_table_accounts=[],
                recent_blockhash=blockhash
            )
            
            # Create and sign transaction
            tx = VersionedTransaction(message, [self.keypair])
            
            # Serialize
            tx_bytes = bytes(tx)
            tx_b64 = base64.b64encode(tx_bytes).decode('utf-8')
            
            logger.info(f"Simple transfer transaction built: {amount_lamports} lamports to {recipient}")
            return tx_b64
            
        except Exception as e:
            logger.error(f"Error building simple transfer: {e}")
            return None
    
    async def build_transaction_from_signal(self, signal: Dict[str, Any]) -> Optional[str]:
        """
        Build a transaction from a trading signal with fallback mechanisms.
        
        Args:
            signal: Trading signal
            
        Returns:
            Signed transaction or None if failed
        """
        try:
            # Try Jupiter swap first
            logger.info("Attempting to build Jupiter swap transaction")
            jupiter_tx = await self.build_jupiter_swap(signal)
            if jupiter_tx:
                return jupiter_tx
            
            # Fallback to simple transfer for testing
            logger.warning("Jupiter swap failed, falling back to simple transfer for testing")
            
            # Create a small test transfer to self
            test_amount = 1000  # 0.000001 SOL
            transfer_tx = await self.build_simple_transfer(self.wallet_address, test_amount)
            if transfer_tx:
                logger.info("Fallback transfer transaction built successfully")
                return transfer_tx
            
            logger.error("All transaction building methods failed")
            return None
            
        except Exception as e:
            logger.error(f"Error building transaction from signal: {e}")
            return None
    
    async def close(self):
        """Close HTTP client and cleanup resources."""
        await self.http_client.aclose()
        logger.info("Enhanced TxBuilder closed")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the transaction builder."""
        return {
            'wallet_address': self.wallet_address,
            'keypair_loaded': self.keypair is not None,
            'pubkey': str(self.pubkey) if self.pubkey else None,
            'solders_available': SOLDERS_AVAILABLE,
            'rpc_url': self.rpc_url,
            'jupiter_api_url': self.jupiter_api_url
        }


async def test_enhanced_tx_builder():
    """Test the enhanced transaction builder."""
    # Test configuration
    wallet_address = os.getenv('WALLET_ADDRESS', 'J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz')
    keypair_path = os.getenv('KEYPAIR_PATH', 'wallet/trading_wallet_keypair.json')
    
    # Create builder
    builder = EnhancedTxBuilder(wallet_address, keypair_path)
    
    try:
        # Test status
        status = builder.get_status()
        logger.info(f"Builder status: {status}")
        
        # Test blockhash retrieval
        blockhash = await builder.get_recent_blockhash()
        logger.info(f"Recent blockhash: {blockhash}")
        
        # Test signal processing
        test_signal = {
            "action": "BUY",
            "market": "SOL-USDC",
            "price": 180.0,
            "size": 0.01,  # Small test size
            "confidence": 0.8,
            "timestamp": datetime.now().isoformat()
        }
        
        # Build transaction
        tx = await builder.build_transaction_from_signal(test_signal)
        if tx:
            logger.info("Transaction built successfully!")
            logger.info(f"Transaction length: {len(tx)} characters")
        else:
            logger.error("Failed to build transaction")
        
    finally:
        await builder.close()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_enhanced_tx_builder())
