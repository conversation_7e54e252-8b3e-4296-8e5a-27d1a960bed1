#!/usr/bin/env python3
"""
Enhanced Transaction Executor with Robust Error Handling and Future-Proofing
Provides comprehensive transaction execution with multiple fallback mechanisms.
"""

import asyncio
import logging
import json
import time
import os
from typing import Dict, Any, Optional, List
from datetime import datetime

import httpx

logger = logging.getLogger(__name__)

class EnhancedTxExecutor:
    """
    Enhanced transaction executor with comprehensive error handling and monitoring.
    Future-proofed with multiple RPC providers and detailed metrics.
    """
    
    def __init__(self, rpc_urls: Optional[List[str]] = None, max_retries: int = 3, 
                 retry_delay: float = 1.0):
        """Initialize the enhanced transaction executor."""
        
        # Default RPC URLs with fallbacks
        self.rpc_urls = rpc_urls or [
            os.getenv('HELIUS_RPC_URL', 'https://api.mainnet-beta.solana.com'),
            'https://api.mainnet-beta.solana.com',
            'https://solana-api.projectserum.com'
        ]
        
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.current_rpc_index = 0
        
        # HTTP client with timeout
        self.http_client = httpx.AsyncClient(timeout=30.0)
        
        # Metrics tracking
        self.metrics = {
            'total_transactions': 0,
            'successful_transactions': 0,
            'failed_transactions': 0,
            'retried_transactions': 0,
            'rpc_failures': {},
            'average_execution_time': 0.0,
            'total_execution_time': 0.0
        }
        
        # Transaction history
        self.tx_history = []
        
        logger.info(f"Enhanced TxExecutor initialized with {len(self.rpc_urls)} RPC URLs")
        logger.info(f"Primary RPC: {self.rpc_urls[0]}")
    
    async def send_transaction(self, transaction: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Send a transaction with comprehensive error handling and retries.
        
        Args:
            transaction: Base64 encoded signed transaction
            options: Transaction options
            
        Returns:
            Result dictionary with success status and details
        """
        start_time = time.time()
        self.metrics['total_transactions'] += 1
        
        options = options or {}
        encoding = options.get('encoding', 'base64')
        commitment = options.get('commitment', 'confirmed')
        
        # Try each RPC URL with retries
        for rpc_attempt in range(len(self.rpc_urls)):
            current_rpc = self.rpc_urls[(self.current_rpc_index + rpc_attempt) % len(self.rpc_urls)]
            
            for retry in range(self.max_retries + 1):
                try:
                    logger.info(f"Sending transaction (RPC: {rpc_attempt + 1}/{len(self.rpc_urls)}, "
                               f"Retry: {retry + 1}/{self.max_retries + 1})")
                    
                    # Prepare RPC request
                    payload = {
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "sendTransaction",
                        "params": [
                            transaction,
                            {
                                "encoding": encoding,
                                "skipPreflight": False,
                                "preflightCommitment": commitment,
                                "maxRetries": 0  # We handle retries ourselves
                            }
                        ]
                    }
                    
                    # Send request
                    response = await self.http_client.post(current_rpc, json=payload)
                    response.raise_for_status()
                    
                    data = response.json()
                    
                    # Check for RPC errors
                    if 'error' in data:
                        error_msg = data['error'].get('message', 'Unknown RPC error')
                        error_code = data['error'].get('code', -1)
                        
                        logger.warning(f"RPC error (code {error_code}): {error_msg}")
                        
                        # Track RPC failures
                        if current_rpc not in self.metrics['rpc_failures']:
                            self.metrics['rpc_failures'][current_rpc] = 0
                        self.metrics['rpc_failures'][current_rpc] += 1
                        
                        # Check if we should retry
                        if self._should_retry_error(error_code, error_msg):
                            if retry < self.max_retries:
                                self.metrics['retried_transactions'] += 1
                                await asyncio.sleep(self.retry_delay * (retry + 1))
                                continue
                            else:
                                # Try next RPC
                                break
                        else:
                            # Non-retryable error
                            execution_time = time.time() - start_time
                            self._record_failure(error_msg, execution_time, current_rpc)
                            return {
                                'success': False,
                                'error': error_msg,
                                'error_code': error_code,
                                'signature': None,
                                'rpc_url': current_rpc,
                                'execution_time': execution_time
                            }
                    
                    # Success case
                    if 'result' in data:
                        signature = data['result']
                        execution_time = time.time() - start_time
                        
                        logger.info(f"Transaction sent successfully: {signature}")
                        
                        # Update metrics
                        self.metrics['successful_transactions'] += 1
                        self.metrics['total_execution_time'] += execution_time
                        self.metrics['average_execution_time'] = (
                            self.metrics['total_execution_time'] / self.metrics['total_transactions']
                        )
                        
                        # Record in history
                        self.tx_history.append({
                            'timestamp': datetime.now().isoformat(),
                            'signature': signature,
                            'status': 'success',
                            'rpc_url': current_rpc,
                            'execution_time': execution_time,
                            'retries': retry
                        })
                        
                        # Wait for confirmation
                        confirmation_result = await self._wait_for_confirmation(signature, current_rpc)
                        
                        return {
                            'success': True,
                            'signature': signature,
                            'rpc_url': current_rpc,
                            'execution_time': execution_time,
                            'retries': retry,
                            'confirmation': confirmation_result
                        }
                    
                except httpx.RequestError as e:
                    logger.warning(f"HTTP request error: {e}")
                    if retry < self.max_retries:
                        self.metrics['retried_transactions'] += 1
                        await asyncio.sleep(self.retry_delay * (retry + 1))
                        continue
                    else:
                        break
                        
                except Exception as e:
                    logger.error(f"Unexpected error: {e}")
                    if retry < self.max_retries:
                        self.metrics['retried_transactions'] += 1
                        await asyncio.sleep(self.retry_delay * (retry + 1))
                        continue
                    else:
                        break
        
        # All attempts failed
        execution_time = time.time() - start_time
        error_msg = "All RPC attempts failed"
        self._record_failure(error_msg, execution_time, "all_rpcs")
        
        return {
            'success': False,
            'error': error_msg,
            'signature': None,
            'rpc_url': None,
            'execution_time': execution_time
        }
    
    async def _wait_for_confirmation(self, signature: str, rpc_url: str, 
                                   timeout: float = 30.0) -> Dict[str, Any]:
        """
        Wait for transaction confirmation.
        
        Args:
            signature: Transaction signature
            rpc_url: RPC URL to use
            timeout: Timeout in seconds
            
        Returns:
            Confirmation result
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getSignatureStatuses",
                    "params": [[signature], {"searchTransactionHistory": True}]
                }
                
                response = await self.http_client.post(rpc_url, json=payload)
                response.raise_for_status()
                
                data = response.json()
                
                if 'result' in data and 'value' in data['result']:
                    statuses = data['result']['value']
                    if statuses and len(statuses) > 0 and statuses[0]:
                        status = statuses[0]
                        
                        if status.get('confirmationStatus') in ['confirmed', 'finalized']:
                            logger.info(f"Transaction confirmed: {signature}")
                            return {
                                'confirmed': True,
                                'status': status.get('confirmationStatus'),
                                'slot': status.get('slot'),
                                'err': status.get('err'),
                                'confirmation_time': time.time() - start_time
                            }
                        elif status.get('err'):
                            logger.error(f"Transaction failed: {status.get('err')}")
                            return {
                                'confirmed': False,
                                'error': status.get('err'),
                                'confirmation_time': time.time() - start_time
                            }
                
                # Wait before next check
                await asyncio.sleep(1.0)
                
            except Exception as e:
                logger.warning(f"Error checking confirmation: {e}")
                await asyncio.sleep(1.0)
        
        # Timeout
        logger.warning(f"Confirmation timeout for transaction: {signature}")
        return {
            'confirmed': False,
            'error': 'Confirmation timeout',
            'confirmation_time': timeout
        }
    
    def _should_retry_error(self, error_code: int, error_msg: str) -> bool:
        """Determine if an error should be retried."""
        
        # Retryable error codes
        retryable_codes = [
            -32005,  # Node is unhealthy
            -32002,  # Transaction simulation failed
            -32003,  # Transaction signature verification failure
        ]
        
        # Retryable error messages
        retryable_messages = [
            'node is unhealthy',
            'transaction was not confirmed',
            'blockhash not found',
            'timeout',
            'network error'
        ]
        
        if error_code in retryable_codes:
            return True
        
        error_msg_lower = error_msg.lower()
        for msg in retryable_messages:
            if msg in error_msg_lower:
                return True
        
        return False
    
    def _record_failure(self, error: str, execution_time: float, rpc_url: str):
        """Record a transaction failure."""
        self.metrics['failed_transactions'] += 1
        self.metrics['total_execution_time'] += execution_time
        if self.metrics['total_transactions'] > 0:
            self.metrics['average_execution_time'] = (
                self.metrics['total_execution_time'] / self.metrics['total_transactions']
            )
        
        self.tx_history.append({
            'timestamp': datetime.now().isoformat(),
            'signature': None,
            'status': 'failure',
            'error': error,
            'rpc_url': rpc_url,
            'execution_time': execution_time
        })
    
    async def simulate_transaction(self, transaction: str, 
                                 options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Simulate a transaction before sending.
        
        Args:
            transaction: Base64 encoded transaction
            options: Simulation options
            
        Returns:
            Simulation result
        """
        options = options or {}
        encoding = options.get('encoding', 'base64')
        commitment = options.get('commitment', 'confirmed')
        
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "simulateTransaction",
                "params": [
                    transaction,
                    {
                        "encoding": encoding,
                        "commitment": commitment,
                        "sigVerify": False,
                        "replaceRecentBlockhash": True
                    }
                ]
            }
            
            # Use primary RPC for simulation
            response = await self.http_client.post(self.rpc_urls[0], json=payload)
            response.raise_for_status()
            
            data = response.json()
            
            if 'error' in data:
                return {
                    'success': False,
                    'error': data['error'].get('message', 'Simulation failed')
                }
            
            if 'result' in data:
                result = data['result']['value']
                return {
                    'success': True,
                    'result': result,
                    'logs': result.get('logs', []),
                    'err': result.get('err')
                }
            
            return {
                'success': False,
                'error': 'Invalid simulation response'
            }
            
        except Exception as e:
            logger.error(f"Error simulating transaction: {e}")
            return {
                'success': False,
                'error': f"Simulation error: {str(e)}"
            }
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get executor metrics."""
        return {
            **self.metrics,
            'rpc_urls': self.rpc_urls,
            'current_rpc_index': self.current_rpc_index,
            'tx_history_length': len(self.tx_history),
            'success_rate': (
                self.metrics['successful_transactions'] / self.metrics['total_transactions']
                if self.metrics['total_transactions'] > 0 else 0
            )
        }
    
    def get_recent_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent transaction history."""
        return self.tx_history[-limit:] if self.tx_history else []
    
    async def close(self):
        """Close HTTP client and cleanup resources."""
        await self.http_client.aclose()
        logger.info("Enhanced TxExecutor closed")


async def test_enhanced_tx_executor():
    """Test the enhanced transaction executor."""
    executor = EnhancedTxExecutor()
    
    try:
        # Test with a dummy transaction (will fail but tests the flow)
        dummy_tx = "dGVzdCB0cmFuc2FjdGlvbg=="  # "test transaction" in base64
        
        result = await executor.send_transaction(dummy_tx)
        logger.info(f"Test result: {result}")
        
        # Get metrics
        metrics = executor.get_metrics()
        logger.info(f"Metrics: {metrics}")
        
    finally:
        await executor.close()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_enhanced_tx_executor())
