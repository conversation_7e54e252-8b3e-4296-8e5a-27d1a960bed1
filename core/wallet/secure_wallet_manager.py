#!/usr/bin/env python3
"""
Secure Wallet Manager with Robust Keypair Handling
Provides secure keypair loading, validation, and management with comprehensive error handling.
"""

import os
import json
import logging
import base58
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
from cryptography.fernet import Fernet
import hashlib

# Solana imports
try:
    from solders.keypair import Keypair
    from solders.pubkey import Pubkey
    SOLDERS_AVAILABLE = True
except ImportError:
    SOLDERS_AVAILABLE = False
    logging.warning("Solders not available")

logger = logging.getLogger(__name__)

class SecureWalletManager:
    """
    Secure wallet manager with comprehensive keypair handling and validation.
    Future-proofed with encryption support and detailed security logging.
    """
    
    def __init__(self, encryption_key: Optional[str] = None):
        """Initialize the secure wallet manager."""
        self.encryption_key = encryption_key
        self.fernet = None
        
        if encryption_key:
            # Initialize encryption
            key_bytes = hashlib.sha256(encryption_key.encode()).digest()
            fernet_key = base64.urlsafe_b64encode(key_bytes)
            self.fernet = Fernet(fernet_key)
            logger.info("Wallet manager initialized with encryption")
        else:
            logger.info("Wallet manager initialized without encryption")
        
        # Loaded keypairs
        self.keypairs = {}
        self.active_keypair = None
        self.active_pubkey = None
    
    def validate_keypair_file(self, keypair_path: str) -> Tuple[bool, str]:
        """
        Validate a keypair file format and accessibility.
        
        Args:
            keypair_path: Path to the keypair file
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Check if file exists
            if not os.path.exists(keypair_path):
                return False, f"Keypair file not found: {keypair_path}"
            
            # Check file permissions
            file_stat = os.stat(keypair_path)
            if file_stat.st_mode & 0o077:
                logger.warning(f"Keypair file has overly permissive permissions: {oct(file_stat.st_mode)}")
            
            # Read and validate file content
            with open(keypair_path, 'r') as f:
                try:
                    data = json.load(f)
                except json.JSONDecodeError as e:
                    return False, f"Invalid JSON in keypair file: {e}"
            
            # Validate format
            if not isinstance(data, list):
                return False, "Keypair file must contain a JSON array"
            
            if len(data) != 64:
                return False, f"Keypair array must have 64 elements, found {len(data)}"
            
            # Validate all elements are integers in valid range
            for i, val in enumerate(data):
                if not isinstance(val, int):
                    return False, f"Element {i} is not an integer: {type(val)}"
                if val < 0 or val > 255:
                    return False, f"Element {i} is out of range [0, 255]: {val}"
            
            return True, "Keypair file is valid"
            
        except Exception as e:
            return False, f"Error validating keypair file: {e}"
    
    def load_keypair(self, name: str, keypair_path: str, 
                    expected_pubkey: Optional[str] = None) -> Tuple[bool, str]:
        """
        Load and validate a keypair from file.
        
        Args:
            name: Name to assign to this keypair
            keypair_path: Path to the keypair file
            expected_pubkey: Optional expected public key for validation
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # Validate file first
            is_valid, error_msg = self.validate_keypair_file(keypair_path)
            if not is_valid:
                return False, error_msg
            
            if not SOLDERS_AVAILABLE:
                return False, "Solders library not available for keypair operations"
            
            # Read keypair data
            with open(keypair_path, 'r') as f:
                keypair_data = json.load(f)
            
            # Create keypair
            keypair_bytes = bytes(keypair_data)
            keypair = Keypair.from_bytes(keypair_bytes)
            pubkey = keypair.pubkey()
            
            # Validate against expected public key if provided
            if expected_pubkey:
                if str(pubkey) != expected_pubkey:
                    return False, f"Keypair public key mismatch. Expected: {expected_pubkey}, Got: {pubkey}"
            
            # Store keypair
            self.keypairs[name] = {
                'keypair': keypair,
                'pubkey': pubkey,
                'path': keypair_path,
                'loaded_at': datetime.now().isoformat()
            }
            
            # Set as active if first keypair
            if not self.active_keypair:
                self.set_active_keypair(name)
            
            logger.info(f"Keypair '{name}' loaded successfully: {pubkey}")
            return True, f"Keypair loaded: {pubkey}"
            
        except Exception as e:
            logger.error(f"Error loading keypair '{name}': {e}")
            return False, f"Failed to load keypair: {e}"
    
    def set_active_keypair(self, name: str) -> bool:
        """
        Set the active keypair for signing operations.
        
        Args:
            name: Name of the keypair to set as active
            
        Returns:
            True if successful, False otherwise
        """
        if name not in self.keypairs:
            logger.error(f"Keypair '{name}' not found")
            return False
        
        self.active_keypair = name
        self.active_pubkey = self.keypairs[name]['pubkey']
        
        logger.info(f"Active keypair set to '{name}': {self.active_pubkey}")
        return True
    
    def get_active_keypair(self) -> Optional[Keypair]:
        """Get the active keypair for signing."""
        if not self.active_keypair or self.active_keypair not in self.keypairs:
            return None
        return self.keypairs[self.active_keypair]['keypair']
    
    def get_active_pubkey(self) -> Optional[Pubkey]:
        """Get the active public key."""
        return self.active_pubkey
    
    def get_active_address(self) -> Optional[str]:
        """Get the active wallet address as string."""
        return str(self.active_pubkey) if self.active_pubkey else None
    
    def create_new_keypair(self, name: str, save_path: Optional[str] = None) -> Tuple[bool, str]:
        """
        Create a new keypair and optionally save it to file.
        
        Args:
            name: Name for the new keypair
            save_path: Optional path to save the keypair
            
        Returns:
            Tuple of (success, message_or_pubkey)
        """
        if not SOLDERS_AVAILABLE:
            return False, "Solders library not available"
        
        try:
            # Generate new keypair
            keypair = Keypair()
            pubkey = keypair.pubkey()
            
            # Store in memory
            self.keypairs[name] = {
                'keypair': keypair,
                'pubkey': pubkey,
                'path': save_path,
                'loaded_at': datetime.now().isoformat()
            }
            
            # Save to file if requested
            if save_path:
                success, msg = self.save_keypair(name, save_path)
                if not success:
                    # Remove from memory if save failed
                    del self.keypairs[name]
                    return False, f"Failed to save keypair: {msg}"
            
            logger.info(f"New keypair '{name}' created: {pubkey}")
            return True, str(pubkey)
            
        except Exception as e:
            logger.error(f"Error creating keypair '{name}': {e}")
            return False, f"Failed to create keypair: {e}"
    
    def save_keypair(self, name: str, save_path: str) -> Tuple[bool, str]:
        """
        Save a keypair to file with secure permissions.
        
        Args:
            name: Name of the keypair to save
            save_path: Path to save the keypair
            
        Returns:
            Tuple of (success, message)
        """
        if name not in self.keypairs:
            return False, f"Keypair '{name}' not found"
        
        try:
            keypair = self.keypairs[name]['keypair']
            
            # Convert keypair to bytes and then to list
            keypair_bytes = bytes(keypair)
            keypair_data = list(keypair_bytes)
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # Save with secure permissions
            with open(save_path, 'w') as f:
                json.dump(keypair_data, f)
            
            # Set secure file permissions (owner read/write only)
            os.chmod(save_path, 0o600)
            
            # Update stored path
            self.keypairs[name]['path'] = save_path
            
            logger.info(f"Keypair '{name}' saved to {save_path}")
            return True, f"Keypair saved to {save_path}"
            
        except Exception as e:
            logger.error(f"Error saving keypair '{name}': {e}")
            return False, f"Failed to save keypair: {e}"
    
    def backup_keypair(self, name: str, backup_path: str, encrypt: bool = True) -> Tuple[bool, str]:
        """
        Create an encrypted backup of a keypair.
        
        Args:
            name: Name of the keypair to backup
            backup_path: Path for the backup file
            encrypt: Whether to encrypt the backup
            
        Returns:
            Tuple of (success, message)
        """
        if name not in self.keypairs:
            return False, f"Keypair '{name}' not found"
        
        if encrypt and not self.fernet:
            return False, "Encryption requested but no encryption key provided"
        
        try:
            keypair = self.keypairs[name]['keypair']
            pubkey = self.keypairs[name]['pubkey']
            
            # Prepare backup data
            backup_data = {
                'name': name,
                'pubkey': str(pubkey),
                'keypair': list(bytes(keypair)),
                'created_at': datetime.now().isoformat(),
                'encrypted': encrypt
            }
            
            # Serialize to JSON
            json_data = json.dumps(backup_data).encode('utf-8')
            
            # Encrypt if requested
            if encrypt:
                json_data = self.fernet.encrypt(json_data)
            
            # Save backup
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            with open(backup_path, 'wb') as f:
                f.write(json_data)
            
            # Set secure permissions
            os.chmod(backup_path, 0o600)
            
            logger.info(f"Keypair '{name}' backed up to {backup_path} (encrypted: {encrypt})")
            return True, f"Backup created: {backup_path}"
            
        except Exception as e:
            logger.error(f"Error backing up keypair '{name}': {e}")
            return False, f"Failed to create backup: {e}"
    
    def list_keypairs(self) -> Dict[str, Dict[str, Any]]:
        """List all loaded keypairs with their information."""
        result = {}
        for name, data in self.keypairs.items():
            result[name] = {
                'pubkey': str(data['pubkey']),
                'path': data['path'],
                'loaded_at': data['loaded_at'],
                'is_active': name == self.active_keypair
            }
        return result
    
    def get_status(self) -> Dict[str, Any]:
        """Get wallet manager status."""
        return {
            'solders_available': SOLDERS_AVAILABLE,
            'encryption_enabled': self.fernet is not None,
            'keypairs_loaded': len(self.keypairs),
            'active_keypair': self.active_keypair,
            'active_address': self.get_active_address(),
            'keypair_names': list(self.keypairs.keys())
        }


# Import datetime here to avoid circular imports
from datetime import datetime

def create_secure_wallet_manager(encryption_password: Optional[str] = None) -> SecureWalletManager:
    """Create a secure wallet manager with optional encryption."""
    return SecureWalletManager(encryption_password)


async def test_secure_wallet_manager():
    """Test the secure wallet manager."""
    # Create manager
    manager = SecureWalletManager()
    
    # Test status
    status = manager.get_status()
    logger.info(f"Manager status: {status}")
    
    # Test keypair loading
    keypair_path = os.getenv('KEYPAIR_PATH', 'wallet/trading_wallet_keypair.json')
    expected_address = os.getenv('WALLET_ADDRESS')
    
    if os.path.exists(keypair_path):
        success, msg = manager.load_keypair('trading', keypair_path, expected_address)
        logger.info(f"Load result: {success}, {msg}")
        
        if success:
            # Test active keypair
            active_address = manager.get_active_address()
            logger.info(f"Active address: {active_address}")
            
            # List keypairs
            keypairs = manager.list_keypairs()
            logger.info(f"Loaded keypairs: {keypairs}")
    else:
        logger.warning(f"Keypair file not found: {keypair_path}")


if __name__ == "__main__":
    import base64
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_secure_wallet_manager())
