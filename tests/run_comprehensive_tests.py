#!/usr/bin/env python3
"""
Comprehensive Test Runner for Synergy7 Trading System
Runs all updated tests and provides detailed reporting.
"""

import os
import sys
import subprocess
import logging
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/comprehensive_test_results.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveTestRunner:
    """Comprehensive test runner for the Synergy7 trading system."""

    def __init__(self):
        """Initialize the test runner."""
        self.test_results = {}
        self.start_time = None
        self.end_time = None

        # Define test suites
        self.test_suites = {
            'production_live_trading': {
                'file': 'tests/test_production_live_trading.py',
                'description': 'Production live trading system tests',
                'critical': True
            },
            'signal_generation': {
                'file': 'tests/test_signal_generation_system.py',
                'description': 'Signal generation and enrichment tests',
                'critical': True
            },
            'transaction_execution': {
                'file': 'tests/test_transaction_execution_system.py',
                'description': 'Transaction building and execution tests',
                'critical': True
            },
            'risk_management': {
                'file': 'tests/test_risk_management_system.py',
                'description': 'Risk management and monitoring tests',
                'critical': True
            },
            'full_system_integration': {
                'file': 'tests/test_full_system_integration.py',
                'description': 'End-to-end system integration tests',
                'critical': True
            },
            'deployment_validation': {
                'file': 'tests/test_deployment_validation.py',
                'description': 'Deployment prerequisites and validation tests',
                'critical': True
            },
            'carbon_core_integration': {
                'file': 'tests/test_carbon_core_integration.py',
                'description': 'Carbon Core and Rust component tests',
                'critical': False
            },
            'monitoring_and_health': {
                'file': 'tests/test_monitoring_and_health.py',
                'description': 'Monitoring, health checks, and alerting tests',
                'critical': True
            },
            'wallet_security': {
                'file': 'tests/test_wallet_security.py',
                'description': 'Wallet security and validation tests',
                'critical': True
            },
            'helius_integration': {
                'file': 'tests/test_helius_integration.py',
                'description': 'Helius RPC integration tests',
                'critical': False
            },
            'transaction_executor': {
                'file': 'tests/test_transaction_executor.py',
                'description': 'Legacy transaction executor tests',
                'critical': False
            }
        }

        # Define deprecated tests to skip
        self.deprecated_tests = [
            'tests/test_momentum_optimizer.py',
            'tests/test_portfolio_limits.py',
            'tests/test_position_sizer.py',
            'tests/test_stop_loss.py',
            'tests/test_circuit_breaker.py',
            'phase_4_deployment/tests/test_liljito_client.py',
            'phase_4_deployment/tests/test_stream_data_ingestor.py',
            'phase_4_deployment/tests/test_streamlit_dashboard.py',
            'phase_4_deployment/tests/test_live_trading.py'
        ]

    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are met for running tests."""
        logger.info("🔍 Checking test prerequisites...")

        # Check Python version
        if sys.version_info < (3, 8):
            logger.error("❌ Python 3.8+ required")
            return False

        # Check required packages
        required_packages = ['pytest', 'pytest-asyncio']
        missing_packages = []

        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)

        if missing_packages:
            logger.error(f"❌ Missing required packages: {', '.join(missing_packages)}")
            logger.info("Install with: pip install pytest pytest-asyncio")
            return False

        # Check test files exist
        missing_files = []
        for suite_name, suite_info in self.test_suites.items():
            test_file = Path(suite_info['file'])
            if not test_file.exists():
                missing_files.append(str(test_file))

        if missing_files:
            logger.error(f"❌ Missing test files: {', '.join(missing_files)}")
            return False

        # Create logs directory
        os.makedirs('logs', exist_ok=True)

        logger.info("✅ All prerequisites met")
        return True

    def run_test_suite(self, suite_name: str, suite_info: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single test suite."""
        logger.info(f"🧪 Running {suite_name}: {suite_info['description']}")

        test_file = suite_info['file']
        start_time = time.time()

        try:
            # Run pytest with detailed output
            cmd = [
                sys.executable, '-m', 'pytest',
                test_file,
                '-v',
                '--tb=short',
                '--json-report',
                f'--json-report-file=logs/{suite_name}_results.json'
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout per suite
            )

            end_time = time.time()
            duration = end_time - start_time

            # Parse results
            success = result.returncode == 0

            # Try to load JSON report
            json_report_file = f'logs/{suite_name}_results.json'
            test_details = {}

            if os.path.exists(json_report_file):
                try:
                    with open(json_report_file, 'r') as f:
                        json_report = json.load(f)

                    test_details = {
                        'total_tests': json_report.get('summary', {}).get('total', 0),
                        'passed': json_report.get('summary', {}).get('passed', 0),
                        'failed': json_report.get('summary', {}).get('failed', 0),
                        'skipped': json_report.get('summary', {}).get('skipped', 0),
                        'errors': json_report.get('summary', {}).get('error', 0)
                    }
                except Exception as e:
                    logger.warning(f"⚠️ Could not parse JSON report for {suite_name}: {e}")

            return {
                'success': success,
                'duration': duration,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'returncode': result.returncode,
                'details': test_details,
                'critical': suite_info.get('critical', False)
            }

        except subprocess.TimeoutExpired:
            logger.error(f"❌ {suite_name} timed out after 5 minutes")
            return {
                'success': False,
                'duration': 300,
                'stdout': '',
                'stderr': 'Test suite timed out',
                'returncode': -1,
                'details': {},
                'critical': suite_info.get('critical', False)
            }
        except Exception as e:
            logger.error(f"❌ Error running {suite_name}: {e}")
            return {
                'success': False,
                'duration': 0,
                'stdout': '',
                'stderr': str(e),
                'returncode': -1,
                'details': {},
                'critical': suite_info.get('critical', False)
            }

    def run_all_tests(self) -> bool:
        """Run all test suites."""
        logger.info("🚀 Starting comprehensive test run...")
        self.start_time = datetime.now()

        # Check prerequisites
        if not self.check_prerequisites():
            return False

        # Run each test suite
        total_suites = len(self.test_suites)
        passed_suites = 0
        failed_critical_suites = []

        for i, (suite_name, suite_info) in enumerate(self.test_suites.items(), 1):
            logger.info(f"📊 Progress: {i}/{total_suites}")

            result = self.run_test_suite(suite_name, suite_info)
            self.test_results[suite_name] = result

            if result['success']:
                passed_suites += 1
                logger.info(f"✅ {suite_name} passed ({result['duration']:.2f}s)")
            else:
                logger.error(f"❌ {suite_name} failed ({result['duration']:.2f}s)")
                if result['critical']:
                    failed_critical_suites.append(suite_name)

        self.end_time = datetime.now()

        # Generate summary
        self.generate_test_report()

        # Determine overall success
        overall_success = len(failed_critical_suites) == 0

        if overall_success:
            logger.info(f"🎉 All critical tests passed! ({passed_suites}/{total_suites} suites passed)")
        else:
            logger.error(f"💥 Critical test failures: {', '.join(failed_critical_suites)}")

        return overall_success

    def generate_test_report(self):
        """Generate comprehensive test report."""
        logger.info("📋 Generating test report...")

        total_duration = (self.end_time - self.start_time).total_seconds()

        # Calculate summary statistics
        total_suites = len(self.test_results)
        passed_suites = sum(1 for r in self.test_results.values() if r['success'])
        failed_suites = total_suites - passed_suites

        total_tests = sum(r['details'].get('total_tests', 0) for r in self.test_results.values())
        passed_tests = sum(r['details'].get('passed', 0) for r in self.test_results.values())
        failed_tests = sum(r['details'].get('failed', 0) for r in self.test_results.values())
        skipped_tests = sum(r['details'].get('skipped', 0) for r in self.test_results.values())

        # Generate report
        report = {
            'timestamp': self.start_time.isoformat(),
            'duration_seconds': total_duration,
            'summary': {
                'total_suites': total_suites,
                'passed_suites': passed_suites,
                'failed_suites': failed_suites,
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'skipped_tests': skipped_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            'suite_results': self.test_results,
            'deprecated_tests': self.deprecated_tests
        }

        # Save detailed report
        report_file = f'logs/comprehensive_test_report_{self.start_time.strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        # Print summary
        logger.info("="*80)
        logger.info("📊 COMPREHENSIVE TEST RESULTS SUMMARY")
        logger.info("="*80)
        logger.info(f"⏱️  Total Duration: {total_duration:.2f} seconds")
        logger.info(f"📦 Test Suites: {passed_suites}/{total_suites} passed")
        logger.info(f"🧪 Individual Tests: {passed_tests}/{total_tests} passed ({report['summary']['success_rate']:.1f}%)")
        logger.info(f"⏭️  Skipped Tests: {skipped_tests}")
        logger.info(f"📄 Detailed Report: {report_file}")
        logger.info("="*80)

        # Print suite details
        for suite_name, result in self.test_results.items():
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            critical = "🔴 CRITICAL" if result['critical'] else "🟡 NON-CRITICAL"
            duration = result['duration']

            logger.info(f"{status} {critical} {suite_name} ({duration:.2f}s)")

            if result['details']:
                details = result['details']
                logger.info(f"    Tests: {details.get('passed', 0)}/{details.get('total_tests', 0)} passed")

        logger.info("="*80)

        # Print deprecated tests
        if self.deprecated_tests:
            logger.info("🗑️  DEPRECATED TESTS (not run):")
            for deprecated_test in self.deprecated_tests:
                logger.info(f"    {deprecated_test}")
            logger.info("="*80)


def main():
    """Main function."""
    import argparse

    parser = argparse.ArgumentParser(description="Comprehensive Test Runner for Synergy7")
    parser.add_argument("--suite", help="Run specific test suite only")
    parser.add_argument("--list", action="store_true", help="List available test suites")

    args = parser.parse_args()

    runner = ComprehensiveTestRunner()

    if args.list:
        print("Available test suites:")
        for suite_name, suite_info in runner.test_suites.items():
            critical = "CRITICAL" if suite_info['critical'] else "NON-CRITICAL"
            print(f"  {suite_name}: {suite_info['description']} ({critical})")
        return 0

    if args.suite:
        if args.suite not in runner.test_suites:
            print(f"Error: Unknown test suite '{args.suite}'")
            print("Use --list to see available suites")
            return 1

        # Run single suite
        suite_info = runner.test_suites[args.suite]
        result = runner.run_test_suite(args.suite, suite_info)

        if result['success']:
            print(f"✅ {args.suite} passed")
            return 0
        else:
            print(f"❌ {args.suite} failed")
            return 1

    # Run all tests
    success = runner.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
