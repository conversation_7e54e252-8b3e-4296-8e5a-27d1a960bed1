#!/usr/bin/env python3
"""
Carbon Core Integration Tests
Tests for Carbon Core binary and Rust component integration.
"""

import pytest
import asyncio
import os
import sys
import subprocess
import logging
from unittest.mock import Mock, patch
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestCarbonCoreBinary:
    """Test Carbon Core binary availability and functionality."""
    
    def test_carbon_core_binary_exists(self):
        """Test that Carbon Core binary exists."""
        possible_paths = [
            'bin/carbon_core',
            'target/release/carbon_core',
            'carbon_core/target/release/carbon_core',
            'phase_4_deployment/bin/carbon_core'
        ]
        
        binary_found = False
        for path in possible_paths:
            if os.path.exists(path):
                binary_found = True
                logger.info(f"✅ Carbon Core binary found: {path}")
                
                # Check if executable
                if os.access(path, os.X_OK):
                    logger.info(f"✅ Carbon Core binary is executable")
                else:
                    logger.warning(f"⚠️ Carbon Core binary is not executable: {path}")
                break
        
        if not binary_found:
            logger.warning("⚠️ Carbon Core binary not found (will use Python fallback)")
    
    def test_carbon_core_execution(self):
        """Test Carbon Core binary execution."""
        possible_paths = [
            'bin/carbon_core',
            'target/release/carbon_core',
            'carbon_core/target/release/carbon_core',
            'phase_4_deployment/bin/carbon_core'
        ]
        
        for path in possible_paths:
            if os.path.exists(path) and os.access(path, os.X_OK):
                try:
                    # Try to run with --help flag
                    result = subprocess.run(
                        [path, '--help'],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )
                    
                    if result.returncode == 0:
                        logger.info(f"✅ Carbon Core binary executes successfully: {path}")
                        return
                    else:
                        logger.warning(f"⚠️ Carbon Core binary execution failed: {result.stderr}")
                        
                except subprocess.TimeoutExpired:
                    logger.warning(f"⚠️ Carbon Core binary execution timed out: {path}")
                except Exception as e:
                    logger.warning(f"⚠️ Error executing Carbon Core binary: {e}")
        
        logger.warning("⚠️ No working Carbon Core binary found (will use Python fallback)")
    
    def test_rust_toolchain_availability(self):
        """Test Rust toolchain availability for building Carbon Core."""
        try:
            # Check if rustc is available
            result = subprocess.run(['rustc', '--version'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info(f"✅ Rust compiler available: {result.stdout.strip()}")
            else:
                logger.warning("⚠️ Rust compiler not available")
            
            # Check if cargo is available
            result = subprocess.run(['cargo', '--version'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info(f"✅ Cargo available: {result.stdout.strip()}")
            else:
                logger.warning("⚠️ Cargo not available")
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.warning("⚠️ Rust toolchain not available (OK if using pre-built binary)")


class TestCarbonCoreManager:
    """Test Carbon Core manager functionality."""
    
    @pytest.mark.asyncio
    async def test_carbon_core_manager_initialization(self):
        """Test Carbon Core manager initialization."""
        try:
            from phase_4_deployment.python_comm_layer.carbon_core_manager import CarbonCoreManager
            
            manager = CarbonCoreManager()
            
            # Test initialization
            assert manager is not None
            logger.info("✅ Carbon Core manager initialized successfully")
            
        except ImportError:
            logger.warning("⚠️ Carbon Core manager not available")
    
    @pytest.mark.asyncio
    async def test_carbon_core_manager_startup(self):
        """Test Carbon Core manager startup process."""
        try:
            from phase_4_deployment.python_comm_layer.carbon_core_manager import CarbonCoreManager
            
            manager = CarbonCoreManager()
            
            # Test startup (should handle missing binary gracefully)
            startup_result = await manager.start()
            
            # Should either succeed or fall back gracefully
            assert isinstance(startup_result, bool)
            
            if startup_result:
                logger.info("✅ Carbon Core started successfully")
                
                # Test health check
                is_healthy = manager.is_healthy()
                assert isinstance(is_healthy, bool)
                logger.info(f"✅ Carbon Core health check: {is_healthy}")
                
                # Test shutdown
                await manager.stop()
                logger.info("✅ Carbon Core stopped successfully")
            else:
                logger.info("✅ Carbon Core startup failed gracefully (using fallback)")
                assert manager.using_fallback == True
            
        except ImportError:
            logger.warning("⚠️ Carbon Core manager not available")
        except Exception as e:
            logger.error(f"❌ Carbon Core manager test failed: {e}")
            # Don't fail the test - fallback should handle this
    
    @pytest.mark.asyncio
    async def test_carbon_core_fallback_mechanism(self):
        """Test Carbon Core fallback mechanism."""
        try:
            from phase_4_deployment.python_comm_layer.carbon_core_manager import CarbonCoreManager
            
            # Force fallback mode
            manager = CarbonCoreManager()
            manager.using_fallback = True
            
            # Test that fallback mode works
            startup_result = await manager.start()
            
            # Fallback should always succeed
            assert startup_result == True
            assert manager.using_fallback == True
            
            logger.info("✅ Carbon Core fallback mechanism working")
            
        except ImportError:
            logger.warning("⚠️ Carbon Core manager not available")


class TestMockCarbonCore:
    """Test Mock Carbon Core functionality."""
    
    @pytest.mark.asyncio
    async def test_mock_carbon_core_initialization(self):
        """Test Mock Carbon Core initialization."""
        try:
            from phase_4_deployment.python_comm_layer.mock_carbon_core import MockCarbonCore
            
            mock_core = MockCarbonCore()
            
            # Test initialization
            assert mock_core is not None
            logger.info("✅ Mock Carbon Core initialized successfully")
            
        except ImportError:
            logger.warning("⚠️ Mock Carbon Core not available")
    
    @pytest.mark.asyncio
    async def test_mock_carbon_core_functionality(self):
        """Test Mock Carbon Core functionality."""
        try:
            from phase_4_deployment.python_comm_layer.mock_carbon_core import MockCarbonCore
            
            mock_core = MockCarbonCore()
            
            # Test basic functionality
            if hasattr(mock_core, 'process_data'):
                test_data = {'test': 'data'}
                result = await mock_core.process_data(test_data)
                assert result is not None
                logger.info("✅ Mock Carbon Core data processing working")
            
            if hasattr(mock_core, 'get_status'):
                status = mock_core.get_status()
                assert status is not None
                logger.info(f"✅ Mock Carbon Core status: {status}")
            
        except ImportError:
            logger.warning("⚠️ Mock Carbon Core not available")
        except Exception as e:
            logger.error(f"❌ Mock Carbon Core test failed: {e}")


class TestSolanaTransactionUtils:
    """Test Solana transaction utilities (Rust components)."""
    
    def test_solana_tx_utils_availability(self):
        """Test Solana transaction utilities availability."""
        try:
            import solana_tx_utils
            logger.info("✅ Solana transaction utilities available")
            
            # Test basic imports
            if hasattr(solana_tx_utils, 'Keypair'):
                logger.info("✅ Keypair class available")
            
            if hasattr(solana_tx_utils, 'Transaction'):
                logger.info("✅ Transaction class available")
                
        except ImportError:
            logger.warning("⚠️ Solana transaction utilities not available (will use Python fallback)")
    
    def test_solana_tx_utils_functionality(self):
        """Test Solana transaction utilities functionality."""
        try:
            from solana_tx_utils import Keypair, Transaction
            
            # Test keypair generation
            keypair = Keypair()
            assert keypair is not None
            
            # Test public key extraction
            pubkey = keypair.pubkey()
            assert pubkey is not None
            assert len(pubkey) > 0
            
            logger.info("✅ Solana transaction utilities working")
            
        except ImportError:
            logger.warning("⚠️ Solana transaction utilities not available")
        except Exception as e:
            logger.error(f"❌ Solana transaction utilities test failed: {e}")
    
    def test_transaction_preparation_service(self):
        """Test transaction preparation service."""
        try:
            from solana_tx_utils.tx_prep import TransactionPreparationService
            
            # Test initialization
            rpc_url = "https://api.mainnet-beta.solana.com"
            service = TransactionPreparationService(rpc_url)
            assert service is not None
            
            logger.info("✅ Transaction preparation service available")
            
        except ImportError:
            logger.warning("⚠️ Transaction preparation service not available")
        except Exception as e:
            logger.error(f"❌ Transaction preparation service test failed: {e}")


class TestRustPythonIntegration:
    """Test Rust-Python integration components."""
    
    def test_pyo3_integration(self):
        """Test PyO3 integration."""
        try:
            # Try to import any Rust modules that use PyO3
            rust_modules = [
                'solana_tx_utils',
                'carbon_core_py',
                'rust_utils'
            ]
            
            available_modules = []
            for module in rust_modules:
                try:
                    __import__(module)
                    available_modules.append(module)
                except ImportError:
                    continue
            
            if available_modules:
                logger.info(f"✅ PyO3 Rust modules available: {available_modules}")
            else:
                logger.warning("⚠️ No PyO3 Rust modules found (using Python fallbacks)")
                
        except Exception as e:
            logger.error(f"❌ PyO3 integration test failed: {e}")
    
    def test_rust_fallback_mechanisms(self):
        """Test Rust fallback mechanisms."""
        # Test that the system can handle missing Rust components
        
        # Mock missing Rust modules
        with patch.dict('sys.modules', {'solana_tx_utils': None}):
            try:
                # Try to import something that should fall back to Python
                from phase_4_deployment.rpc_execution.tx_builder import TxBuilder
                
                # Should work even without Rust components
                wallet_address = 'J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz'
                tx_builder = TxBuilder(wallet_address)
                assert tx_builder is not None
                
                logger.info("✅ Rust fallback mechanisms working")
                
            except Exception as e:
                logger.error(f"❌ Rust fallback test failed: {e}")


class TestPerformanceComparison:
    """Test performance comparison between Rust and Python implementations."""
    
    def test_transaction_building_performance(self):
        """Test transaction building performance."""
        import time
        
        # Test data
        test_signal = {
            'action': 'BUY',
            'market': 'SOL-USDC',
            'price': 180.0,
            'size': 0.1
        }
        
        wallet_address = 'J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz'
        
        try:
            from phase_4_deployment.rpc_execution.tx_builder import TxBuilder
            
            # Test Python implementation
            start_time = time.time()
            tx_builder = TxBuilder(wallet_address)
            python_init_time = time.time() - start_time
            
            logger.info(f"✅ Transaction builder initialization time: {python_init_time:.4f}s")
            
            # Performance should be reasonable
            assert python_init_time < 1.0, f"Transaction builder initialization too slow: {python_init_time:.4f}s"
            
        except Exception as e:
            logger.error(f"❌ Performance test failed: {e}")
    
    def test_keypair_generation_performance(self):
        """Test keypair generation performance."""
        import time
        
        # Test Python fallback performance
        start_time = time.time()
        
        # Generate multiple keypairs to test performance
        for _ in range(10):
            try:
                # Try Rust implementation first
                from solana_tx_utils import Keypair
                keypair = Keypair()
                break
            except ImportError:
                # Fall back to Python implementation
                import secrets
                keypair_bytes = secrets.token_bytes(32)
                break
        
        generation_time = time.time() - start_time
        
        logger.info(f"✅ Keypair generation time (10 keypairs): {generation_time:.4f}s")
        
        # Performance should be reasonable
        assert generation_time < 5.0, f"Keypair generation too slow: {generation_time:.4f}s"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
