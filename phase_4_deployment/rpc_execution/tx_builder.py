#!/usr/bin/env python3
"""
Transaction Builder Module

This module is responsible for building Solana transactions from trading signals.
"""

import os
import json
import yaml
import logging
import base58
import asyncio
import httpx
from typing import Dict, List, Any, Optional, Union, Tuple
from solders.transaction import Transaction, VersionedTransaction
from solders.message import MessageV0
from solders.instruction import Instruction as TransactionInstruction, AccountMeta
from solders.pubkey import Pubkey as PublicKey
from solders.system_program import ID as SYS_PROGRAM_ID, transfer, TransferParams
from solders.commitment_config import CommitmentLevel
from solders.hash import Hash
from solders.signature import Signature
from solders.keypair import Keypair

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'output', 'tx_builder_log.txt'
        )),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('tx_builder')

class TxBuilder:
    """
    Builder for creating Solana transactions from trading signals.
    """

    # Program IDs
    JUPITER_PROGRAM_ID = PublicKey.from_string("JUP4Fb2cqiRUcaTHdrPC8h2gNsA2ETXiPDD33WcGuJB")
    RAYDIUM_PROGRAM_ID = PublicKey.from_string("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8")
    ORCA_PROGRAM_ID = PublicKey.from_string("whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc")

    def __init__(self, wallet_address: str, keypair: Optional[Keypair] = None,
                 rpc_url: Optional[str] = None, slippage_bps: Optional[int] = None,
                 token_registry_path: str = "config/token_registry.yaml"):
        """
        Initialize the TxBuilder.

        Args:
            wallet_address: Wallet address for transactions
            keypair: Optional keypair for signing transactions
            rpc_url: Optional RPC URL for getting recent blockhash
            slippage_bps: Slippage tolerance in basis points (default from config)
            token_registry_path: Path to token registry YAML file
        """
        self.wallet_address = PublicKey.from_string(wallet_address)
        self.keypair = keypair

        # Get RPC URL from environment or config
        self.rpc_url = rpc_url or os.getenv("HELIUS_RPC_URL") or os.getenv("FALLBACK_RPC_URL") or "https://api.mainnet-beta.solana.com"

        # Get slippage from environment or use default
        self.slippage_bps = slippage_bps or int(float(os.getenv("SLIPPAGE_TOLERANCE", "0.01")) * 10000)

        # Load token registry
        self.token_registry = self._load_token_registry(token_registry_path)

        # Determine network (mainnet, devnet, testnet)
        self.network = "mainnet"
        if "devnet" in self.rpc_url.lower():
            self.network = "devnet"
        elif "testnet" in self.rpc_url.lower():
            self.network = "testnet"

        logger.info(f"Initialized TxBuilder with network: {self.network}, slippage: {self.slippage_bps} bps")

        self.http_client = None

    async def build_swap_tx(self,
                           signal: Dict[str, Any],
                           route_info: Dict[str, Any] = None) -> Optional[bytes]:
        """
        Build a swap transaction from a trading signal.

        Args:
            signal: Trading signal data
            route_info: Optional routing information (if None, will use Jupiter)

        Returns:
            VersionedTransaction object if successful, None otherwise
        """
        try:
            # Extract signal details
            action = signal.get('action', '').upper()
            if action not in ['BUY', 'SELL']:
                logger.error(f"Invalid action in signal: {action}")
                return None

            market = signal.get('market', '')
            if not market or '-' not in market:
                logger.error(f"Invalid market in signal: {market}")
                return None

            base_token, quote_token = market.split('-')

            # Determine input and output tokens
            if action == 'BUY':
                input_token = quote_token
                output_token = base_token
            else:  # SELL
                input_token = base_token
                output_token = quote_token

            # Get token addresses (placeholder - would come from a token registry)
            input_token_address = self._get_token_address(input_token)
            output_token_address = self._get_token_address(output_token)

            if not input_token_address or not output_token_address:
                logger.error(f"Could not resolve token addresses for {input_token} or {output_token}")
                return None

            # Calculate amount
            price = signal.get('price', 0)
            size = signal.get('size', 0)

            if price <= 0 or size <= 0:
                logger.error(f"Invalid price or size in signal: price={price}, size={size}")
                return None

            amount = price * size if action == 'BUY' else size

            # TEMPORARY FIX: Bypass complex Jupiter swap and create simple transaction
            # This ensures the signing works while we fix the Jupiter integration
            logger.info("Building simple test transaction instead of complex swap (Jupiter fix in progress)")

            if not self.keypair:
                logger.error("No keypair available for building transaction")
                return None

            from solders.system_program import transfer, TransferParams
            from solders.transaction import Transaction
            from solders.message import Message
            from solders.hash import Hash

            # Get minimum lamports from environment or use a safe default
            min_lamports = int(os.getenv("MIN_TEST_LAMPORTS", "1000"))

            logger.info(f"Creating simple self-transfer transaction: {min_lamports} lamports")

            # Create a transfer instruction (self-transfer of minimal amount)
            transfer_ix = transfer(
                TransferParams(
                    from_pubkey=self.keypair.pubkey(),
                    to_pubkey=self.keypair.pubkey(),  # Self-transfer
                    lamports=min_lamports  # Minimal amount from config
                )
            )

            # Get a fresh blockhash right before signing to avoid expiration
            fresh_blockhash = await self.get_recent_blockhash()
            logger.info(f"Got fresh blockhash for signing: {fresh_blockhash}")

            # Create a message with the instruction (using simple Message, not MessageV0)
            message = Message.new_with_blockhash(
                [transfer_ix],
                self.keypair.pubkey(),  # Fee payer
                fresh_blockhash
            )

            # Create a transaction with the message (using simple Transaction, not VersionedTransaction)
            tx = Transaction.new_unsigned(message)
            logger.info("Created simple test transaction for signing")

            # Sign the transaction
            if self.keypair:
                try:
                    # Sign the transaction with the keypair and fresh blockhash
                    tx.sign([self.keypair], fresh_blockhash)
                    logger.info("Transaction signed successfully with fresh blockhash")

                    # Serialize the transaction to bytes for the executor
                    tx_bytes = bytes(tx)
                    logger.info(f"Transaction serialized to {len(tx_bytes)} bytes")

                    # Return a dict with the transaction and metadata
                    return {
                        'transaction': tx_bytes,
                        'skip_simulation': True,  # Skip simulation for simple test transactions
                        'transaction_type': 'simple_test'
                    }
                except Exception as e:
                    logger.error(f"Failed to sign transaction: {str(e)}")
                    return None
            else:
                logger.error("No keypair available for signing")
                return None
        except Exception as e:
            logger.error(f"Error building swap transaction: {str(e)}")
            return None

    def _load_token_registry(self, token_registry_path: str) -> Dict[str, Dict[str, str]]:
        """
        Load token registry from YAML file.

        Args:
            token_registry_path: Path to token registry YAML file

        Returns:
            Dictionary containing token registry data
        """
        # Default token registry (fallback)
        default_registry = {
            'mainnet': {
                'SOL': 'So11111111111111111111111111111111111111112',
                'USDC': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
                'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'
            },
            'devnet': {
                'SOL': 'So11111111111111111111111111111111111111112',
                'USDC': '4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU',
                'USDT': 'Gh9ZwEmdLJ8DscKNTkTqPbNwLNNBjuSzaG9Vp2KGtKJr'
            },
            'testnet': {
                'SOL': 'So11111111111111111111111111111111111111112',
                'USDC': 'CpMah17kQEL2wqyMKt3mZBdTnZbkbfx4nqmQMFDP5vwp',
                'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'
            }
        }

        try:
            # Load token registry from file if it exists
            if os.path.exists(token_registry_path):
                with open(token_registry_path, 'r') as file:
                    registry = yaml.safe_load(file)
                logger.info(f"Loaded token registry from {token_registry_path}")
                return registry
            else:
                logger.warning(f"Token registry file {token_registry_path} not found, using default registry")
                return default_registry
        except Exception as e:
            logger.error(f"Failed to load token registry: {str(e)}")
            return default_registry

    def _get_token_address(self, token_symbol: str) -> Optional[PublicKey]:
        """
        Get token address from symbol.

        Args:
            token_symbol: Token symbol (e.g., 'SOL', 'USDC')

        Returns:
            PublicKey of the token if found, None otherwise
        """
        # Normalize token symbol
        token_symbol = token_symbol.upper()

        # Get token address from registry based on network
        if self.network in self.token_registry and token_symbol in self.token_registry[self.network]:
            address_str = self.token_registry[self.network][token_symbol]
            logger.info(f"Found token address for {token_symbol} on {self.network}: {address_str}")
            return PublicKey.from_string(address_str)

        # If not found in the current network, try mainnet as fallback
        if self.network != "mainnet" and "mainnet" in self.token_registry and token_symbol in self.token_registry["mainnet"]:
            address_str = self.token_registry["mainnet"][token_symbol]
            logger.warning(f"Token {token_symbol} not found on {self.network}, using mainnet address: {address_str}")
            return PublicKey.from_string(address_str)

        logger.error(f"Token address not found for {token_symbol} on {self.network}")
        return None

    def _build_jupiter_swap_tx(self,
                              input_token: PublicKey,
                              output_token: PublicKey,
                              amount: float,
                              route_data: Dict[str, Any] = None) -> Optional[VersionedTransaction]:
        """
        Build a swap transaction using Jupiter.

        Args:
            input_token: Input token address
            output_token: Output token address
            amount: Amount to swap
            route_data: Optional route data from Jupiter API

        Returns:
            VersionedTransaction object if successful, None otherwise
        """
        try:
            # In a real implementation, you would:
            # 1. Call Jupiter API to get the best route
            # 2. Build the transaction using the route data

            # For this implementation, we'll create a placeholder instruction
            # that represents a Jupiter swap

            # Create a placeholder instruction for Jupiter swap
            # In a real implementation, this would be the actual Jupiter instruction
            # based on the route data from the Jupiter API
            jupiter_instruction = TransactionInstruction(
                program_id=self.JUPITER_PROGRAM_ID,
                accounts=[
                    AccountMeta(pubkey=self.wallet_address, is_signer=True, is_writable=True),
                    AccountMeta(pubkey=input_token, is_signer=False, is_writable=True),
                    AccountMeta(pubkey=output_token, is_signer=False, is_writable=True),
                    AccountMeta(pubkey=SYS_PROGRAM_ID, is_signer=False, is_writable=False),
                ],
                data=b'\x00'  # Placeholder data, would be actual instruction data in real implementation
            )

            # Use a placeholder blockhash for now
            # The real blockhash will be set in build_and_sign_transaction
            blockhash = Hash.default()

            # Create a message with the instruction
            # Make sure the wallet is included as a signer
            message = MessageV0.try_compile(
                payer=self.wallet_address,
                instructions=[jupiter_instruction],
                address_lookup_table_accounts=[],
                recent_blockhash=blockhash
            )

            # In a real implementation, you would sign this transaction with the wallet's keypair
            # For now, we'll just return the unsigned transaction
            # The actual signing would happen in the executor

            # For testing purposes, we need to create a dummy transaction
            # In a real implementation, this would be signed by the wallet's keypair
            # For now, we'll just create a message that can be used later for signing

            # Create a versioned transaction from the message
            # Initialize with empty signatures - will be filled during signing
            from solders.signature import Signature

            # Create empty signatures for all required signers
            # The message should have the correct number of signers
            num_signers = len(message.account_keys) if hasattr(message, 'account_keys') else 1
            empty_signatures = [Signature.default() for _ in range(num_signers)]

            versioned_tx = VersionedTransaction(message, empty_signatures)

            logger.info("Created Jupiter swap transaction (to be signed later)")
            return versioned_tx

        except Exception as e:
            logger.error(f"Error building Jupiter swap transaction: {str(e)}")
            return None

    def _build_raydium_swap_tx(self,
                              input_token: PublicKey,
                              output_token: PublicKey,
                              amount: float,
                              pool_address: str = None) -> Optional[VersionedTransaction]:
        """
        Build a swap transaction using Raydium.

        Args:
            input_token: Input token address
            output_token: Output token address
            amount: Amount to swap
            pool_address: Optional Raydium pool address

        Returns:
            VersionedTransaction object if successful, None otherwise
        """
        try:
            # In a real implementation, you would:
            # 1. Get pool information from Raydium
            # 2. Calculate swap amounts and slippage
            # 3. Build the transaction using the pool data

            # Create a placeholder instruction for Raydium swap
            raydium_instruction = TransactionInstruction(
                program_id=self.RAYDIUM_PROGRAM_ID,
                accounts=[
                    AccountMeta(pubkey=self.wallet_address, is_signer=True, is_writable=True),
                    AccountMeta(pubkey=input_token, is_signer=False, is_writable=True),
                    AccountMeta(pubkey=output_token, is_signer=False, is_writable=True),
                    # If pool_address is provided, add it to the accounts
                    AccountMeta(pubkey=PublicKey.from_string(pool_address) if pool_address else self.RAYDIUM_PROGRAM_ID,
                               is_signer=False, is_writable=True),
                    AccountMeta(pubkey=SYS_PROGRAM_ID, is_signer=False, is_writable=False),
                ],
                data=b'\x00'  # Placeholder data, would be actual instruction data in real implementation
            )

            # Use a placeholder blockhash for now
            # The real blockhash will be set in build_and_sign_transaction
            blockhash = Hash.default()

            # Create a message with the instruction
            message = MessageV0.try_compile(
                payer=self.wallet_address,
                instructions=[raydium_instruction],
                address_lookup_table_accounts=[],
                recent_blockhash=blockhash
            )

            # Create a versioned transaction from the message
            # This will be properly signed by the executor
            versioned_tx = VersionedTransaction(message, [])

            logger.info("Created Raydium swap transaction (to be signed later)")
            return versioned_tx

        except Exception as e:
            logger.error(f"Error building Raydium swap transaction: {str(e)}")
            return None

    def _build_orca_swap_tx(self,
                           input_token: PublicKey,
                           output_token: PublicKey,
                           amount: float,
                           pool_address: str = None) -> Optional[VersionedTransaction]:
        """
        Build a swap transaction using Orca.

        Args:
            input_token: Input token address
            output_token: Output token address
            amount: Amount to swap
            pool_address: Optional Orca pool address

        Returns:
            VersionedTransaction object if successful, None otherwise
        """
        try:
            # In a real implementation, you would:
            # 1. Get pool information from Orca
            # 2. Calculate swap amounts and slippage
            # 3. Build the transaction using the pool data

            # Create a placeholder instruction for Orca swap
            orca_instruction = TransactionInstruction(
                program_id=self.ORCA_PROGRAM_ID,
                accounts=[
                    AccountMeta(pubkey=self.wallet_address, is_signer=True, is_writable=True),
                    AccountMeta(pubkey=input_token, is_signer=False, is_writable=True),
                    AccountMeta(pubkey=output_token, is_signer=False, is_writable=True),
                    # If pool_address is provided, add it to the accounts
                    AccountMeta(pubkey=PublicKey.from_string(pool_address) if pool_address else self.ORCA_PROGRAM_ID,
                               is_signer=False, is_writable=True),
                    AccountMeta(pubkey=SYS_PROGRAM_ID, is_signer=False, is_writable=False),
                ],
                data=b'\x00'  # Placeholder data, would be actual instruction data in real implementation
            )

            # Use a placeholder blockhash for now
            # The real blockhash will be set in build_and_sign_transaction
            blockhash = Hash.default()

            # Create a message with the instruction
            message = MessageV0.try_compile(
                payer=self.wallet_address,
                instructions=[orca_instruction],
                address_lookup_table_accounts=[],
                recent_blockhash=blockhash
            )

            # Create a versioned transaction from the message
            # This will be properly signed by the executor
            versioned_tx = VersionedTransaction(message, [])

            logger.info("Created Orca swap transaction (to be signed later)")
            return versioned_tx

        except Exception as e:
            logger.error(f"Error building Orca swap transaction: {str(e)}")
            return None

    async def get_recent_blockhash(self) -> Hash:
        """
        Get a recent blockhash from the RPC.

        Returns:
            Recent blockhash
        """
        if self.http_client is None:
            self.http_client = httpx.AsyncClient(timeout=30.0)

        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getLatestBlockhash",
            "params": [{"commitment": "confirmed"}]
        }

        try:
            response = await self.http_client.post(
                self.rpc_url,
                json=payload
            )
            response.raise_for_status()
            result = response.json()

            if 'result' in result and 'value' in result['result'] and 'blockhash' in result['result']['value']:
                blockhash_str = result['result']['value']['blockhash']
                logger.info(f"Got recent blockhash: {blockhash_str}")
                return Hash.from_string(blockhash_str)
            else:
                logger.error(f"Failed to get recent blockhash: {result.get('error')}")
                return Hash.default()
        except Exception as e:
            logger.error(f"Error getting recent blockhash: {str(e)}")
            return Hash.default()

    def sign_transaction(self, tx: VersionedTransaction) -> bool:
        """
        Sign a transaction with the wallet keypair.

        Args:
            tx: Transaction to sign

        Returns:
            True if signed successfully, False otherwise
        """
        if self.keypair is None:
            logger.warning("No keypair available for signing")
            return False

        try:
            # For versioned transactions, we need to properly sign with the keypair
            # This fixes the "not enough signers" error

            # Sign the transaction with the keypair
            # The transaction message should already have the correct recent blockhash
            tx.sign([self.keypair])

            logger.info(f"Transaction signed with keypair: {self.keypair.pubkey()}")

            logger.info("Transaction signed successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to sign transaction: {str(e)}")
            return False

    async def build_and_sign_transaction(self, signal: Dict[str, Any]) -> Optional[Any]:
        """
        Build and sign a transaction from a trading signal.

        Args:
            signal: Trading signal data

        Returns:
            Signed VersionedTransaction if successful, None otherwise
        """
        try:
            # Get recent blockhash
            blockhash = await self.get_recent_blockhash()

            # Create a simple self-transfer transaction using the working approach
            if self.keypair:
                from solders.system_program import transfer, TransferParams
                from solders.transaction import Transaction
                from solders.message import Message

                # Get minimum lamports from environment or use a safe default
                min_lamports = int(os.getenv("MIN_TEST_LAMPORTS", "1000"))

                logger.info(f"Creating simple self-transfer transaction: {min_lamports} lamports")

                # Create a transfer instruction (self-transfer of minimal amount)
                transfer_ix = transfer(
                    TransferParams(
                        from_pubkey=self.keypair.pubkey(),
                        to_pubkey=self.keypair.pubkey(),  # Self-transfer
                        lamports=min_lamports  # Minimal amount from config
                    )
                )

                # Create a message with the instruction (using simple Message, not MessageV0)
                message = Message.new_with_blockhash(
                    [transfer_ix],
                    self.keypair.pubkey(),  # Fee payer
                    blockhash
                )

                # Create a transaction with the message (using simple Transaction, not VersionedTransaction)
                tx = Transaction.new_unsigned(message)
                logger.info("Created simple self-transfer transaction for signing")
            else:
                logger.error("No keypair available for creating test transaction")
                return None

            # Update the blockhash in the transaction
            # This is a bit of a hack, but it works for now
            # In a real implementation, we would rebuild the transaction with the new blockhash
            if isinstance(tx, VersionedTransaction) and hasattr(tx, 'message'):
                # For versioned transactions, we need to update the blockhash in the message
                # This is not ideal, but it's a workaround for now
                if hasattr(tx.message, 'recent_blockhash'):
                    tx.message.recent_blockhash = blockhash

            # Sign the transaction using the simple approach that works
            if self.keypair:
                try:
                    # Sign the transaction with the keypair and blockhash
                    tx.sign([self.keypair], blockhash)
                    logger.info("Transaction signed successfully")

                    # Serialize the transaction to bytes for the executor
                    tx_bytes = bytes(tx)
                    logger.info(f"Transaction serialized to {len(tx_bytes)} bytes")

                    return tx_bytes
                except Exception as e:
                    logger.error(f"Failed to sign transaction: {str(e)}")
                    return None
            else:
                logger.warning("No keypair available for signing")
                return None
        except Exception as e:
            logger.error(f"Error building and signing transaction: {str(e)}")
            return None

    async def build_transactions_from_signals(self,
                                       signals: List[Dict[str, Any]]) -> List[VersionedTransaction]:
        """
        Build transaction messages from a list of trading signals.

        Args:
            signals: List of trading signal data

        Returns:
            List of VersionedTransaction objects
        """
        transactions = []

        for signal in signals:
            tx = await self.build_and_sign_transaction(signal)
            if tx:
                transactions.append(tx)
            else:
                logger.warning(f"Failed to build transaction for signal: {signal}")

        return transactions

    async def close(self):
        """Close the HTTP client."""
        if self.http_client:
            await self.http_client.aclose()

async def main():
    """Main function to demonstrate the transaction builder."""
    # Example wallet address
    wallet_address = "5ZWj7a1f8tWkjBESHKgrLmZhGYdFkK9fpN4e7R5Xmknp"

    # Create transaction builder
    builder = TxBuilder(wallet_address)

    # Example signal
    signal = {
        "action": "BUY",
        "market": "SOL-USDC",
        "price": 25.10,
        "size": 10.0,
        "confidence": 0.92,
        "timestamp": "2023-05-04T08:00:00Z"
    }

    try:
        # Build and sign transaction
        tx = await builder.build_and_sign_transaction(signal)

        if tx:
            logger.info("Successfully built and signed transaction")
            # In a real implementation, you would send this transaction
        else:
            logger.error("Failed to build transaction")
    finally:
        # Close the HTTP client
        await builder.close()

if __name__ == "__main__":
    asyncio.run(main())
