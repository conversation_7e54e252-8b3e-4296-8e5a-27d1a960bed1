#!/usr/bin/env python3
"""
Import Wallet Script - Convert various wallet formats to Synergy7 format
"""

import json
import os
import base58
from solders.keypair import Keypair
from solders.pubkey import Pub<PERSON>

def import_from_base58_private_key(private_key_base58: str, output_path: str = "wallet/imported_wallet_keypair.json"):
    """Import wallet from Base58 private key."""
    try:
        # Decode Base58 private key
        private_key_bytes = base58.b58decode(private_key_base58)
        
        # Create keypair
        keypair = Keypair.from_bytes(private_key_bytes)
        pubkey = keypair.pubkey()
        
        # Convert to JSON array format
        keypair_array = list(bytes(keypair))
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Save keypair
        with open(output_path, 'w') as f:
            json.dump(keypair_array, f)
        
        # Set secure permissions
        os.chmod(output_path, 0o600)
        
        print(f"✅ Wallet imported successfully!")
        print(f"   Public Key: {pubkey}")
        print(f"   Keypair saved to: {output_path}")
        print(f"   File permissions: 600 (secure)")
        print()
        print(f"📝 Update your .env file:")
        print(f"   WALLET_ADDRESS={pubkey}")
        print(f"   KEYPAIR_PATH={output_path}")
        
        return str(pubkey), output_path
        
    except Exception as e:
        print(f"❌ Error importing wallet: {e}")
        return None, None

def import_from_json_file(input_path: str, output_path: str = "wallet/imported_wallet_keypair.json"):
    """Import wallet from existing JSON keypair file."""
    try:
        # Read existing keypair
        with open(input_path, 'r') as f:
            keypair_data = json.load(f)
        
        # Validate format
        if not isinstance(keypair_data, list) or len(keypair_data) != 64:
            raise ValueError("Invalid keypair format")
        
        # Create keypair to validate
        keypair = Keypair.from_bytes(bytes(keypair_data))
        pubkey = keypair.pubkey()
        
        # Copy to output location
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(keypair_data, f)
        
        # Set secure permissions
        os.chmod(output_path, 0o600)
        
        print(f"✅ Wallet imported successfully!")
        print(f"   Public Key: {pubkey}")
        print(f"   Keypair copied to: {output_path}")
        print(f"   File permissions: 600 (secure)")
        print()
        print(f"📝 Update your .env file:")
        print(f"   WALLET_ADDRESS={pubkey}")
        print(f"   KEYPAIR_PATH={output_path}")
        
        return str(pubkey), output_path
        
    except Exception as e:
        print(f"❌ Error importing wallet: {e}")
        return None, None

def import_from_phantom_export(private_key_array: list, output_path: str = "wallet/imported_wallet_keypair.json"):
    """Import wallet from Phantom wallet export (array format)."""
    try:
        # Validate format
        if not isinstance(private_key_array, list) or len(private_key_array) != 64:
            raise ValueError("Invalid private key array format")
        
        # Create keypair to validate
        keypair = Keypair.from_bytes(bytes(private_key_array))
        pubkey = keypair.pubkey()
        
        # Save keypair
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(private_key_array, f)
        
        # Set secure permissions
        os.chmod(output_path, 0o600)
        
        print(f"✅ Wallet imported successfully!")
        print(f"   Public Key: {pubkey}")
        print(f"   Keypair saved to: {output_path}")
        print(f"   File permissions: 600 (secure)")
        print()
        print(f"📝 Update your .env file:")
        print(f"   WALLET_ADDRESS={pubkey}")
        print(f"   KEYPAIR_PATH={output_path}")
        
        return str(pubkey), output_path
        
    except Exception as e:
        print(f"❌ Error importing wallet: {e}")
        return None, None

def update_env_file(wallet_address: str, keypair_path: str):
    """Update .env file with new wallet configuration."""
    try:
        # Read current .env
        with open('.env', 'r') as f:
            lines = f.readlines()
        
        # Update wallet configuration
        updated_lines = []
        for line in lines:
            if line.startswith('WALLET_ADDRESS='):
                updated_lines.append(f'WALLET_ADDRESS={wallet_address}\n')
            elif line.startswith('KEYPAIR_PATH='):
                updated_lines.append(f'KEYPAIR_PATH={keypair_path}\n')
            else:
                updated_lines.append(line)
        
        # Write updated .env
        with open('.env', 'w') as f:
            f.writelines(updated_lines)
        
        print(f"✅ .env file updated successfully!")
        
    except Exception as e:
        print(f"❌ Error updating .env file: {e}")

def main():
    """Interactive wallet import."""
    print("🔐 Synergy7 Wallet Import Tool")
    print("=" * 40)
    print()
    print("Choose import method:")
    print("1. Base58 Private Key (from Solflare, etc.)")
    print("2. JSON Keypair File (from Solana CLI)")
    print("3. Private Key Array (from Phantom export)")
    print("4. Update .env only (if keypair already exists)")
    print()
    
    choice = input("Enter choice (1-4): ").strip()
    
    if choice == "1":
        private_key = input("Enter Base58 private key: ").strip()
        if private_key:
            pubkey, path = import_from_base58_private_key(private_key)
            if pubkey and path:
                update_choice = input("\nUpdate .env file automatically? (y/n): ").strip().lower()
                if update_choice == 'y':
                    update_env_file(pubkey, path)
    
    elif choice == "2":
        input_path = input("Enter path to JSON keypair file: ").strip()
        if input_path and os.path.exists(input_path):
            pubkey, path = import_from_json_file(input_path)
            if pubkey and path:
                update_choice = input("\nUpdate .env file automatically? (y/n): ").strip().lower()
                if update_choice == 'y':
                    update_env_file(pubkey, path)
        else:
            print("❌ File not found")
    
    elif choice == "3":
        print("Enter private key array (64 numbers separated by commas):")
        array_input = input("Array: ").strip()
        try:
            # Parse array input
            private_key_array = [int(x.strip()) for x in array_input.split(',')]
            pubkey, path = import_from_phantom_export(private_key_array)
            if pubkey and path:
                update_choice = input("\nUpdate .env file automatically? (y/n): ").strip().lower()
                if update_choice == 'y':
                    update_env_file(pubkey, path)
        except ValueError:
            print("❌ Invalid array format")
    
    elif choice == "4":
        wallet_address = input("Enter wallet address: ").strip()
        keypair_path = input("Enter keypair path: ").strip()
        if wallet_address and keypair_path:
            update_env_file(wallet_address, keypair_path)
    
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
