#!/usr/bin/env python3
"""
RL-Enhanced Live Trading System
Extends the enhanced live trading system with reinforcement learning capabilities.
The system learns and adapts over time to improve profitability.
"""

import asyncio
import argparse
import logging
import sys
import os
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import base enhanced live trading system
from scripts.enhanced_live_trading import EnhancedLiveTradingSystem

# Import RL components
from core.learning.adaptive_strategy_manager import AdaptiveStrategyManager

logger = logging.getLogger(__name__)

class RLEnhancedLiveTradingSystem(EnhancedLiveTradingSystem):
    """Enhanced live trading system with reinforcement learning capabilities."""
    
    def __init__(self, config_path: str = "config/live_production.yaml"):
        """Initialize RL-enhanced live trading system."""
        super().__init__(config_path)
        
        # RL components
        self.adaptive_manager = None
        self.learning_enabled = self.config.get('adaptive_learning', {}).get('enabled', False)
        
        # Learning metrics
        self.session_start_balance = None
        self.last_balance = None
        self.trade_pnl_history = []
        
        logger.info(f"RL-Enhanced Live Trading System initialized (Learning: {'ON' if self.learning_enabled else 'OFF'})")
    
    async def initialize_components(self) -> bool:
        """Initialize all components including RL components."""
        
        # Initialize base components
        if not await super().initialize_components():
            return False
        
        # Initialize adaptive strategy manager
        if self.learning_enabled:
            self.adaptive_manager = AdaptiveStrategyManager(self.config)
            logger.info("✅ Adaptive Strategy Manager initialized")
            
            # Set initial balance for PnL tracking
            self.session_start_balance = await self.wallet_manager.get_balance()
            self.last_balance = self.session_start_balance
            
            # Send learning status notification
            if self.telegram_notifier and self.telegram_notifier.enabled:
                await self.telegram_notifier.send_message(
                    "🧠 *RL-ENHANCED TRADING ACTIVATED* 🧠\n\n"
                    "✅ Adaptive learning enabled\n"
                    "✅ Strategy optimization active\n"
                    "✅ Performance-based adaptation\n\n"
                    "The system will learn and improve over time! 🚀"
                )
        else:
            logger.info("ℹ️ Adaptive learning disabled")
        
        return True
    
    async def execute_trading_cycle(self) -> Dict[str, Any]:
        """Execute a trading cycle with RL enhancements."""
        
        cycle_data = {
            'cycle_id': f"cycle_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'timestamp': datetime.now().isoformat(),
            'status': 'started',
            'learning_enabled': self.learning_enabled
        }
        
        try:
            # Get base trading signal
            signal = await self.generate_trading_signal()
            cycle_data['base_signal'] = signal
            
            if not signal or signal.get('action') == 'HOLD':
                cycle_data['status'] = 'no_signal'
                return cycle_data
            
            # Apply RL adaptations if learning is enabled
            if self.learning_enabled and self.adaptive_manager:
                adapted_signal = self.adaptive_manager.get_adapted_signal(signal)
                cycle_data['adapted_signal'] = adapted_signal
                
                # Log adaptation
                if adapted_signal.get('adapted', False):
                    logger.info(f"🧠 Signal adapted: {signal['action']} -> {adapted_signal['action']}")
                    if adapted_signal.get('adaptation_reason'):
                        logger.info(f"   Reason: {adapted_signal['adaptation_reason']}")
                
                signal = adapted_signal
            
            # If signal was adapted to HOLD, skip execution
            if signal.get('action') == 'HOLD':
                cycle_data['status'] = 'adapted_to_hold'
                return cycle_data
            
            # Calculate position size
            position_data = await self.position_sizer.calculate_position_size(signal)
            cycle_data['position_data'] = position_data
            
            # Check for rejection
            if position_data.get('rejected', False):
                rejection_reason = position_data.get('rejection_reason', 'Unknown reason')
                logger.warning(f"Position rejected: {rejection_reason}")
                
                # Record rejection for learning
                if self.learning_enabled and self.adaptive_manager:
                    self.adaptive_manager.record_trade_result(
                        signal, 
                        {'success': False, 'rejection_reason': rejection_reason}, 
                        0.0
                    )
                
                # Send Telegram notification for rejected trade
                if self.telegram_notifier and self.telegram_notifier.enabled:
                    await self.telegram_notifier.notify_trade_rejected(signal, rejection_reason)
                
                cycle_data['status'] = 'rejected'
                cycle_data['rejection_reason'] = rejection_reason
                self.metrics['trades_rejected'] += 1
                return cycle_data
            
            # Build and execute transaction
            transaction = await self.tx_builder.build_transaction(signal)
            
            if not transaction:
                logger.error("Failed to build transaction")
                cycle_data['status'] = 'build_failed'
                return cycle_data
            
            # Execute transaction
            result = await self.tx_executor.send_transaction(transaction)
            cycle_data['transaction_result'] = result
            
            # Calculate PnL for learning
            trade_pnl = 0.0
            if self.learning_enabled and result.get('success', False):
                current_balance = await self.wallet_manager.get_balance()
                trade_pnl = current_balance - self.last_balance
                self.last_balance = current_balance
                self.trade_pnl_history.append(trade_pnl)
            
            if result.get('success', False):
                logger.info(f"Transaction executed successfully: {result.get('signature')}")
                
                self.trades_executed += 1
                self.metrics['trades_executed'] += 1
                
                # Record successful trade for learning
                if self.learning_enabled and self.adaptive_manager:
                    self.adaptive_manager.record_trade_result(signal, result, trade_pnl)
                
                # Record trade data
                trade_data = {
                    'signal': signal,
                    'position_data': position_data,
                    'transaction_result': result,
                    'timestamp': datetime.now().isoformat(),
                    'trade_pnl': trade_pnl,
                    'learning_enabled': self.learning_enabled
                }
                
                # Save trade data
                await self._save_trade_data(trade_data)
                
                # Send Telegram notification for successful trade
                if self.telegram_notifier and self.telegram_notifier.enabled:
                    await self.telegram_notifier.notify_trade_executed(trade_data)
                
                cycle_data['status'] = 'completed'
                cycle_data['trade_executed'] = True
                cycle_data['signature'] = result.get('signature')
                cycle_data['execution_time'] = result.get('execution_time')
                cycle_data['trade_pnl'] = trade_pnl
                
            else:
                logger.error(f"Transaction failed: {result.get('error')}")
                
                # Record failed trade for learning
                if self.learning_enabled and self.adaptive_manager:
                    self.adaptive_manager.record_trade_result(signal, result, 0.0)
                
                cycle_data['status'] = 'execution_failed'
                cycle_data['error'] = result.get('error')
            
            return cycle_data
            
        except Exception as e:
            logger.error(f"Error in trading cycle: {e}")
            cycle_data['status'] = 'error'
            cycle_data['error'] = str(e)
            return cycle_data
    
    async def _save_enhanced_metrics(self):
        """Save enhanced metrics including learning data."""
        
        # Get base metrics
        await super()._save_enhanced_metrics()
        
        # Add learning metrics if enabled
        if self.learning_enabled and self.adaptive_manager:
            learning_metrics = self.adaptive_manager.get_learning_metrics()
            
            # Calculate session PnL
            session_pnl = 0.0
            if self.session_start_balance and self.last_balance:
                session_pnl = self.last_balance - self.session_start_balance
            
            enhanced_metrics = {
                'timestamp': datetime.now().isoformat(),
                'session_start': self.session_start.isoformat(),
                'session_duration_minutes': (datetime.now() - self.session_start).total_seconds() / 60,
                'learning_enabled': True,
                'session_pnl': session_pnl,
                'trade_pnl_history': self.trade_pnl_history,
                'learning_metrics': learning_metrics,
                'base_metrics': self.metrics,
                'executor_metrics': self.tx_executor.get_metrics() if self.tx_executor else {}
            }
            
            # Save learning-enhanced metrics
            os.makedirs("output/rl_enhanced_live_trading", exist_ok=True)
            
            with open("output/rl_enhanced_live_trading/latest_metrics.json", 'w') as f:
                json.dump(enhanced_metrics, f, indent=2)
            
            # Save learning state
            learning_state_path = f"output/rl_enhanced_live_trading/learning_state_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(learning_state_path, 'w') as f:
                json.dump(learning_metrics, f, indent=2)
    
    async def run_trading_session(self, duration_hours: Optional[float] = None, cycle_interval: float = 30.0) -> bool:
        """Run RL-enhanced trading session."""
        
        logger.info("🧠 Starting RL-Enhanced Live Trading System")
        logger.info(f"Learning enabled: {self.learning_enabled}")
        logger.info(f"Session duration: {duration_hours} hours")
        logger.info(f"Cycle interval: {cycle_interval} seconds")
        
        # Initialize components
        if not await self.initialize_components():
            logger.error("Failed to initialize components")
            return False
        
        # Send session start notification with learning status
        if self.telegram_notifier and self.telegram_notifier.enabled:
            current_balance = await self.wallet_manager.get_balance()
            learning_status = "🧠 RL-ENHANCED" if self.learning_enabled else "STANDARD"
            await self.telegram_notifier.notify_session_started(duration_hours, current_balance)
            
            if self.learning_enabled:
                # Send learning metrics
                learning_metrics = self.adaptive_manager.get_learning_metrics()
                await self.telegram_notifier.send_message(
                    f"🧠 *LEARNING STATUS*\n\n"
                    f"📊 Total Trades Learned: {learning_metrics['total_trades']}\n"
                    f"📈 Win Rate: {learning_metrics['win_rate']:.1%}\n"
                    f"💰 Avg PnL: {learning_metrics['avg_pnl']:+.6f} SOL\n"
                    f"🎯 Strategy Weights: {learning_metrics['strategy_weights']}\n\n"
                    f"System will adapt based on performance! 🚀"
                )
        
        # Run the trading session (use parent class method)
        return await super().run_trading_session(duration_hours, cycle_interval)


async def main():
    """Main function for RL-enhanced live trading."""
    
    parser = argparse.ArgumentParser(description="RL-Enhanced Live Trading System")
    parser.add_argument("--duration", type=float, default=None,
                       help="Trading session duration in hours")
    parser.add_argument("--interval", type=float, default=30.0,
                       help="Cycle interval in seconds")
    parser.add_argument("--config", type=str, default="config/live_production.yaml",
                       help="Configuration file path")
    parser.add_argument("--disable-learning", action="store_true",
                       help="Disable adaptive learning for this session")
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create and run RL-enhanced trading system
    trading_system = RLEnhancedLiveTradingSystem(args.config)
    
    # Disable learning if requested
    if args.disable_learning:
        trading_system.learning_enabled = False
        logger.info("Adaptive learning disabled by command line argument")
    
    try:
        success = await trading_system.run_trading_session(args.duration, args.interval)
        
        if success:
            logger.info("✅ RL-Enhanced trading session completed successfully")
            return 0
        else:
            logger.error("❌ RL-Enhanced trading session failed")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Trading session interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"Fatal error in RL-enhanced trading session: {e}")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
