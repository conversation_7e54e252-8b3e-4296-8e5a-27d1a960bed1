#!/usr/bin/env python3
"""
Emergency Position Flattener
Standalone script to immediately flatten all open positions.
Use this if the trading system stops unexpectedly and leaves positions open.
"""

import asyncio
import sys
import os
import argparse
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.transaction.enhanced_tx_builder import EnhancedTxBuilder
from core.transaction.enhanced_tx_executor import EnhancedTxExecutor
from core.wallet.secure_wallet_manager import SecureWalletManager
from core.risk.position_flattener import PositionFlattener
from core.notifications.telegram_notifier import TelegramNotifier
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def emergency_flatten_positions(force: bool = False, current_price: float = 180.0):
    """Emergency position flattening function."""

    print("🚨 EMERGENCY POSITION FLATTENER")
    print("=" * 40)
    print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💰 Using price: ${current_price:.2f}")
    print()

    # Initialize components
    print("🔄 Initializing components...")

    try:
        # Initialize wallet manager
        wallet_address = os.getenv('WALLET_ADDRESS')
        wallet_manager = SecureWalletManager(wallet_address)
        print("✅ Wallet manager initialized")

        # Initialize transaction builder
        tx_builder = EnhancedTxBuilder()
        await tx_builder.initialize()
        print("✅ Transaction builder initialized")

        # Initialize transaction executor
        rpc_urls = [
            "https://mainnet.helius-rpc.com/?api-key=dda9f776-9a40-447d-9ca4-22a27c21169e",
            "https://api.mainnet-beta.solana.com"
        ]
        tx_executor = EnhancedTxExecutor(rpc_urls)
        print("✅ Transaction executor initialized")

        # Initialize Telegram notifier
        telegram_notifier = TelegramNotifier()
        if telegram_notifier.enabled:
            print("✅ Telegram notifier initialized")
        else:
            print("⚠️ Telegram notifier disabled (no credentials)")

        # Initialize position flattener
        position_flattener = PositionFlattener(
            wallet_manager,
            tx_builder,
            tx_executor,
            telegram_notifier
        )
        print("✅ Position flattener initialized")
        print()

        # Check current wallet balance
        current_balance = await wallet_manager.get_balance()
        print(f"💰 Current wallet balance: {current_balance:.6f} SOL")
        print()

        # Send emergency alert
        if telegram_notifier.enabled:
            await telegram_notifier.send_message(
                "🚨 *EMERGENCY POSITION FLATTENER ACTIVATED* 🚨\n\n"
                "Analyzing positions and executing emergency flattening if needed..."
            )

        # Execute position analysis and flattening
        if force:
            print("⚠️ FORCE MODE: Flattening all positions regardless of risk assessment")
            result = await position_flattener.emergency_flatten_all(current_price)
        else:
            print("🔄 Analyzing positions and risk...")
            result = await position_flattener.execute_position_flattening(current_price)

        print()
        print("📊 FLATTENING RESULTS:")
        print("=" * 30)

        position_analysis = result.get('position_analysis', {})
        risk_assessment = result.get('risk_assessment', {})

        print(f"📈 Net Position: {position_analysis.get('net_position', 0):+.6f} SOL")
        print(f"📊 Buy Volume: {position_analysis.get('buy_volume', 0):.6f} SOL")
        print(f"📊 Sell Volume: {position_analysis.get('sell_volume', 0):.6f} SOL")
        print(f"🎯 Total Trades: {position_analysis.get('total_trades', 0)}")
        print()

        if risk_assessment:
            print(f"⚠️ Risk Level: {risk_assessment.get('risk_level', 'UNKNOWN')}")
            print(f"💵 Position Value: ${risk_assessment.get('position_value_usd', 0):.2f}")
            print()

        if result.get('flattening_executed', False):
            print("✅ POSITION FLATTENING EXECUTED SUCCESSFULLY!")
            tx_result = result.get('transaction_result', {})
            if tx_result.get('signature'):
                print(f"🔗 Transaction: {tx_result['signature']}")
            print("🛡️ All positions have been closed to prevent further risk.")
        else:
            reason = result.get('reason', 'unknown')
            print(f"ℹ️ No flattening executed: {reason}")

            if reason == 'risk_acceptable':
                print("✅ Current position is within acceptable risk limits.")
            elif reason == 'no_action_needed':
                print("✅ No significant position found.")
            else:
                print("⚠️ Flattening was needed but failed to execute.")

        print()

        # Final balance check
        final_balance = await wallet_manager.get_balance()
        balance_change = final_balance - current_balance

        print(f"💰 Final balance: {final_balance:.6f} SOL")
        print(f"📊 Balance change: {balance_change:+.6f} SOL")

        if abs(balance_change) > 0.001:
            print(f"💸 Estimated cost: ${abs(balance_change) * current_price:.2f}")

        # Cleanup
        await tx_builder.close()
        await tx_executor.close()
        if telegram_notifier:
            await telegram_notifier.close()

        print()
        print("🏁 Emergency position flattening completed!")

        return result.get('flattening_executed', False)

    except Exception as e:
        print(f"❌ CRITICAL ERROR: {e}")

        # Send emergency Telegram alert
        try:
            emergency_notifier = TelegramNotifier()
            if emergency_notifier.enabled:
                await emergency_notifier.notify_error(
                    f"EMERGENCY POSITION FLATTENER FAILED: {e}",
                    "CRITICAL SYSTEM ERROR"
                )
                await emergency_notifier.close()
        except:
            pass

        return False

def main():
    """Main function for emergency position flattener."""

    parser = argparse.ArgumentParser(description="Emergency Position Flattener")
    parser.add_argument("--force", action="store_true",
                       help="Force flatten all positions regardless of risk assessment")
    parser.add_argument("--price", type=float, default=180.0,
                       help="Current SOL price for calculations (default: $180)")
    parser.add_argument("--confirm", action="store_true",
                       help="Skip confirmation prompt")

    args = parser.parse_args()

    print("🚨 EMERGENCY POSITION FLATTENER")
    print("=" * 50)
    print()
    print("⚠️ WARNING: This script will analyze and potentially close")
    print("   all open trading positions to prevent further losses.")
    print()
    print(f"🔧 Configuration:")
    print(f"   Force mode: {'YES' if args.force else 'NO'}")
    print(f"   SOL price: ${args.price:.2f}")
    print()

    if not args.confirm:
        response = input("Do you want to proceed? (yes/no): ").strip().lower()
        if response not in ['yes', 'y']:
            print("❌ Operation cancelled by user")
            return 1

    print("🚀 Starting emergency position flattening...")
    print()

    try:
        success = asyncio.run(emergency_flatten_positions(args.force, args.price))

        if success:
            print("✅ Emergency position flattening completed successfully!")
            return 0
        else:
            print("⚠️ No positions were flattened (may be normal)")
            return 0

    except KeyboardInterrupt:
        print("\n❌ Operation cancelled by user")
        return 1
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
