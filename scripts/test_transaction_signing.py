#!/usr/bin/env python3
"""
Test Transaction Signing
Quick test to verify transaction signing is working correctly.
"""

import asyncio
import logging
import json
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_transaction_signing():
    """Test transaction signing functionality."""
    logger.info("🧪 Testing transaction signing functionality...")
    
    # Get environment variables
    wallet_address = os.getenv('WALLET_ADDRESS')
    keypair_path = os.getenv('KEYPAIR_PATH')
    
    if not wallet_address or not keypair_path:
        logger.error("❌ Missing WALLET_ADDRESS or KEYPAIR_PATH environment variables")
        return False
    
    logger.info(f"Wallet: {wallet_address}")
    logger.info(f"Keypair path: {keypair_path}")
    
    try:
        # Load keypair using solders (same as production script)
        from solders.keypair import Keypair
        
        if not os.path.exists(keypair_path):
            logger.error(f"❌ Keypair file not found: {keypair_path}")
            return False
        
        with open(keypair_path, 'r') as f:
            keypair_data = json.load(f)
        
        if isinstance(keypair_data, list) and len(keypair_data) == 64:
            keypair_bytes = bytes(keypair_data)
            keypair = Keypair.from_bytes(keypair_bytes)
            logger.info(f"✅ Keypair loaded successfully: {keypair.pubkey()}")
        else:
            logger.error(f"❌ Invalid keypair format: expected 64-byte array, got {type(keypair_data)} with length {len(keypair_data) if isinstance(keypair_data, list) else 'N/A'}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Error loading keypair: {str(e)}")
        return False
    
    try:
        # Test transaction builder with keypair
        from phase_4_deployment.rpc_execution.tx_builder import TxBuilder
        
        tx_builder = TxBuilder(wallet_address, keypair=keypair)
        logger.info("✅ Transaction builder initialized with keypair")
        
        # Create a test signal
        test_signal = {
            "action": "BUY",
            "market": "SOL-USDC",
            "price": 180.0,
            "size": 0.01,  # Small test amount
            "confidence": 0.8,
            "timestamp": "2025-05-24T14:00:00Z"
        }
        
        logger.info("🔨 Building and signing test transaction...")
        
        # Build and sign transaction
        signed_tx = await tx_builder.build_and_sign_transaction(test_signal)
        
        if signed_tx:
            logger.info("✅ Transaction built and signed successfully!")
            logger.info(f"Transaction type: {type(signed_tx)}")
            
            # Check if transaction has signatures
            if hasattr(signed_tx, 'signatures') and signed_tx.signatures:
                logger.info(f"✅ Transaction has {len(signed_tx.signatures)} signature(s)")
                for i, sig in enumerate(signed_tx.signatures):
                    logger.info(f"   Signature {i+1}: {sig}")
            else:
                logger.warning("⚠️ Transaction has no signatures")
            
            # Test serialization
            try:
                serialized = bytes(signed_tx)
                logger.info(f"✅ Transaction serialized successfully: {len(serialized)} bytes")
            except Exception as e:
                logger.error(f"❌ Error serializing transaction: {str(e)}")
            
            return True
        else:
            logger.error("❌ Failed to build and sign transaction")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing transaction signing: {str(e)}")
        return False
    
    finally:
        # Cleanup
        if 'tx_builder' in locals():
            await tx_builder.close()

async def test_solana_tx_utils_signing():
    """Test solana_tx_utils signing functionality."""
    logger.info("🧪 Testing solana_tx_utils signing...")
    
    try:
        # Test the solana_tx_utils signing functions
        from solana_tx_utils import sign_transaction, Keypair as SolanaKeypair
        
        logger.info("✅ solana_tx_utils imports successful")
        
        # Create a test keypair
        test_keypair = SolanaKeypair()
        logger.info(f"✅ Test keypair created: {test_keypair.pubkey()}")
        
        # Create dummy transaction bytes for testing
        dummy_tx_bytes = b"dummy_transaction_for_signing_test"
        
        # Test signing
        try:
            signed_bytes = sign_transaction(
                dummy_tx_bytes,
                test_keypair.to_bytes(),
                is_versioned=True
            )
            logger.info(f"✅ solana_tx_utils signing successful: {len(signed_bytes)} bytes")
            return True
        except Exception as e:
            logger.error(f"❌ solana_tx_utils signing failed: {str(e)}")
            return False
            
    except ImportError as e:
        logger.warning(f"⚠️ solana_tx_utils not available: {str(e)}")
        return True  # Not a failure, just not available
    except Exception as e:
        logger.error(f"❌ Error testing solana_tx_utils: {str(e)}")
        return False

async def test_production_ready_trader():
    """Test the production ready trader initialization."""
    logger.info("🧪 Testing production ready trader...")
    
    try:
        from scripts.production_ready_trader import ProductionReadyTrader
        
        trader = ProductionReadyTrader()
        logger.info("✅ ProductionReadyTrader created")
        
        # Test component initialization
        success = await trader.initialize_all_components()
        
        if success:
            logger.info("✅ Production components initialized successfully")
            
            # Test building a transaction
            test_signal = {
                "action": "BUY",
                "market": "SOL-USDC",
                "price": 180.0,
                "size": 0.01,
                "confidence": 0.8,
                "timestamp": "2025-05-24T14:00:00Z"
            }
            
            transaction = await trader.build_production_jupiter_transaction(test_signal)
            
            if transaction:
                logger.info("✅ Production Jupiter transaction built successfully")
                return True
            else:
                logger.warning("⚠️ Production Jupiter transaction build failed (may be expected)")
                return True  # Not necessarily a failure
        else:
            logger.error("❌ Failed to initialize production components")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing production ready trader: {str(e)}")
        return False

async def main():
    """Main test function."""
    logger.info("🚀 TRANSACTION SIGNING TEST SUITE")
    logger.info("="*60)
    
    tests = [
        ("Basic Transaction Signing", test_transaction_signing),
        ("Solana TX Utils Signing", test_solana_tx_utils_signing),
        ("Production Ready Trader", test_production_ready_trader)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        logger.info("-" * 40)
        
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {str(e)}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1
    
    logger.info("-" * 60)
    logger.info(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED - Transaction signing is working!")
        return 0
    else:
        logger.error(f"💥 {total - passed} test(s) failed - Transaction signing needs attention")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
