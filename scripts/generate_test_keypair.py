#!/usr/bin/env python3
"""
Generate a valid test keypair for testing purposes.
"""

import json
import os
from solders.keypair import Keypair

def generate_test_keypair():
    """Generate a valid test keypair."""
    # Generate a new keypair
    keypair = Keypair()
    pubkey = keypair.pubkey()
    
    # Convert to bytes and then to list for JSON serialization
    keypair_bytes = bytes(keypair)
    keypair_data = list(keypair_bytes)
    
    print(f"Generated test keypair:")
    print(f"Public key: {pubkey}")
    print(f"Keypair data length: {len(keypair_data)}")
    
    # Save to file
    os.makedirs("wallet", exist_ok=True)
    with open("wallet/trading_wallet_keypair.json", "w") as f:
        json.dump(keypair_data, f)
    
    # Set secure permissions
    os.chmod("wallet/trading_wallet_keypair.json", 0o600)
    
    print(f"Keypair saved to wallet/trading_wallet_keypair.json")
    print(f"Update your .env file with:")
    print(f"WALLET_ADDRESS={pubkey}")
    
    return str(pubkey)

if __name__ == "__main__":
    generate_test_keypair()
