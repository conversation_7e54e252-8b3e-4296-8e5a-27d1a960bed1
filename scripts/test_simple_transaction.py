#!/usr/bin/env python3
"""
Test Simple Transaction
Create and sign a simple transaction using the correct solders approach.
"""

import asyncio
import logging
import json
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_simple_transaction():
    """Test creating and signing a simple transaction."""
    logger.info("🧪 Testing simple transaction creation and signing...")
    
    # Get environment variables
    wallet_address = os.getenv('WALLET_ADDRESS')
    keypair_path = os.getenv('KEYPAIR_PATH')
    
    if not wallet_address or not keypair_path:
        logger.error("❌ Missing WALLET_ADDRESS or KEYPAIR_PATH environment variables")
        return False
    
    try:
        # Load keypair
        from solders.keypair import Keypair
        
        with open(keypair_path, 'r') as f:
            keypair_data = json.load(f)
        
        if isinstance(keypair_data, list) and len(keypair_data) == 64:
            keypair_bytes = bytes(keypair_data)
            keypair = Keypair.from_bytes(keypair_bytes)
            logger.info(f"✅ Keypair loaded: {keypair.pubkey()}")
        else:
            logger.error(f"❌ Invalid keypair format")
            return False
        
    except Exception as e:
        logger.error(f"❌ Error loading keypair: {str(e)}")
        return False
    
    try:
        # Import required solders components
        from solders.hash import Hash
        from solders.system_program import transfer, TransferParams
        from solders.transaction import Transaction
        from solders.message import Message
        from solders.pubkey import Pubkey
        
        # Get recent blockhash
        import httpx
        
        helius_api_key = os.getenv('HELIUS_API_KEY')
        rpc_url = f"https://mainnet.helius-rpc.com/?api-key={helius_api_key}"
        
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getLatestBlockhash",
            "params": [{"commitment": "confirmed"}]
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(rpc_url, json=payload)
            response.raise_for_status()
            result = response.json()
        
        if 'result' in result and 'value' in result['result'] and 'blockhash' in result['result']['value']:
            blockhash_str = result['result']['value']['blockhash']
            logger.info(f"✅ Got recent blockhash: {blockhash_str}")
            blockhash = Hash.from_string(blockhash_str)
        else:
            logger.error(f"❌ Failed to get recent blockhash: {result.get('error')}")
            return False
        
        # Create a simple self-transfer instruction
        transfer_ix = transfer(
            TransferParams(
                from_pubkey=keypair.pubkey(),
                to_pubkey=keypair.pubkey(),  # Self-transfer
                lamports=1000  # 0.000001 SOL
            )
        )
        
        logger.info("✅ Transfer instruction created")
        
        # Create a message with the instruction
        message = Message.new_with_blockhash(
            [transfer_ix],
            keypair.pubkey(),  # Fee payer
            blockhash
        )
        
        logger.info("✅ Message created")
        
        # Create a transaction with the message
        transaction = Transaction.new_unsigned(message)
        
        logger.info("✅ Unsigned transaction created")
        
        # Sign the transaction
        transaction.sign([keypair], blockhash)
        
        logger.info("✅ Transaction signed successfully!")
        
        # Verify the transaction has signatures
        if transaction.signatures:
            logger.info(f"✅ Transaction has {len(transaction.signatures)} signature(s)")
            for i, sig in enumerate(transaction.signatures):
                logger.info(f"   Signature {i+1}: {sig}")
        else:
            logger.error("❌ Transaction has no signatures")
            return False
        
        # Test serialization
        try:
            serialized = bytes(transaction)
            logger.info(f"✅ Transaction serialized successfully: {len(serialized)} bytes")
        except Exception as e:
            logger.error(f"❌ Error serializing transaction: {str(e)}")
            return False
        
        # Test base58 encoding (for RPC submission)
        try:
            import base58
            base58_encoded = base58.b58encode(serialized).decode('utf-8')
            logger.info(f"✅ Transaction base58 encoded: {len(base58_encoded)} characters")
        except Exception as e:
            logger.error(f"❌ Error base58 encoding: {str(e)}")
            return False
        
        logger.info("🎉 Simple transaction creation and signing test PASSED!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in simple transaction test: {str(e)}")
        return False

async def test_versioned_transaction():
    """Test creating and signing a versioned transaction."""
    logger.info("🧪 Testing versioned transaction creation and signing...")
    
    # Get environment variables
    wallet_address = os.getenv('WALLET_ADDRESS')
    keypair_path = os.getenv('KEYPAIR_PATH')
    
    try:
        # Load keypair
        from solders.keypair import Keypair
        
        with open(keypair_path, 'r') as f:
            keypair_data = json.load(f)
        
        keypair_bytes = bytes(keypair_data)
        keypair = Keypair.from_bytes(keypair_bytes)
        
        # Import required components for versioned transactions
        from solders.hash import Hash
        from solders.system_program import transfer, TransferParams
        from solders.transaction import VersionedTransaction
        from solders.message import MessageV0
        from solders.pubkey import Pubkey
        
        # Get recent blockhash
        import httpx
        
        helius_api_key = os.getenv('HELIUS_API_KEY')
        rpc_url = f"https://mainnet.helius-rpc.com/?api-key={helius_api_key}"
        
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getLatestBlockhash",
            "params": [{"commitment": "confirmed"}]
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(rpc_url, json=payload)
            response.raise_for_status()
            result = response.json()
        
        blockhash_str = result['result']['value']['blockhash']
        blockhash = Hash.from_string(blockhash_str)
        
        # Create a simple self-transfer instruction
        transfer_ix = transfer(
            TransferParams(
                from_pubkey=keypair.pubkey(),
                to_pubkey=keypair.pubkey(),
                lamports=1000
            )
        )
        
        # Create a versioned message
        message = MessageV0.try_compile(
            payer=keypair.pubkey(),
            instructions=[transfer_ix],
            address_lookup_table_accounts=[],
            recent_blockhash=blockhash
        )
        
        logger.info("✅ Versioned message created")
        
        # Create a versioned transaction
        versioned_tx = VersionedTransaction(message, [])
        
        logger.info("✅ Versioned transaction created")
        
        # Sign the versioned transaction
        versioned_tx.sign([keypair])
        
        logger.info("✅ Versioned transaction signed successfully!")
        
        # Verify signatures
        if versioned_tx.signatures:
            logger.info(f"✅ Versioned transaction has {len(versioned_tx.signatures)} signature(s)")
        else:
            logger.error("❌ Versioned transaction has no signatures")
            return False
        
        # Test serialization
        try:
            serialized = bytes(versioned_tx)
            logger.info(f"✅ Versioned transaction serialized: {len(serialized)} bytes")
        except Exception as e:
            logger.error(f"❌ Error serializing versioned transaction: {str(e)}")
            return False
        
        logger.info("🎉 Versioned transaction test PASSED!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in versioned transaction test: {str(e)}")
        return False

async def main():
    """Main test function."""
    logger.info("🚀 SIMPLE TRANSACTION SIGNING TEST")
    logger.info("="*50)
    
    tests = [
        ("Simple Transaction", test_simple_transaction),
        ("Versioned Transaction", test_versioned_transaction)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        logger.info("-" * 30)
        
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {str(e)}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("📊 TEST RESULTS")
    logger.info("="*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED - Transaction signing is working!")
        return 0
    else:
        logger.error(f"💥 {total - passed} test(s) failed")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
