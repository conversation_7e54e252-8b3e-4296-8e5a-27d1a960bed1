#!/usr/bin/env python3
"""
Live Trading Status Checker
Verifies that the enhanced live trading system is running and generating data.
"""

import json
import os
import glob
from datetime import datetime
import subprocess

def check_live_trading_process():
    """Check if enhanced live trading process is running."""
    try:
        result = subprocess.run(
            ['ps', 'aux'], 
            capture_output=True, 
            text=True
        )
        
        processes = [line for line in result.stdout.split('\n') if 'enhanced_live_trading' in line and 'grep' not in line]
        
        if processes:
            print("✅ Enhanced Live Trading Process Status:")
            for process in processes:
                parts = process.split()
                pid = parts[1]
                cpu = parts[2]
                mem = parts[3]
                time = parts[9]
                print(f"   PID: {pid}, CPU: {cpu}%, Memory: {mem}%, Runtime: {time}")
            return True
        else:
            print("❌ No enhanced live trading process found")
            return False
            
    except Exception as e:
        print(f"❌ Error checking process: {e}")
        return False

def check_latest_metrics():
    """Check the latest metrics file."""
    try:
        metrics_file = "output/enhanced_live_trading/latest_metrics.json"
        
        if not os.path.exists(metrics_file):
            print("❌ Latest metrics file not found")
            return False
        
        with open(metrics_file, 'r') as f:
            metrics = json.load(f)
        
        print("✅ Latest Metrics Status:")
        print(f"   Session Start: {metrics.get('session_start', 'Unknown')}")
        print(f"   Session Duration: {metrics.get('session_duration_minutes', 0):.1f} minutes")
        print(f"   Cycles Completed: {metrics.get('metrics', {}).get('cycles_completed', 0)}")
        print(f"   Trades Executed: {metrics.get('metrics', {}).get('trades_executed', 0)}")
        print(f"   Transaction Success Rate: {metrics.get('executor_metrics', {}).get('success_rate', 0)*100:.1f}%")
        print(f"   Last Updated: {metrics.get('timestamp', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading metrics: {e}")
        return False

def check_recent_trades():
    """Check recent trade files."""
    try:
        trade_files = glob.glob("output/enhanced_live_trading/trades/trade_*.json")
        
        if not trade_files:
            print("❌ No trade files found")
            return False
        
        # Sort by modification time (most recent first)
        trade_files.sort(key=os.path.getmtime, reverse=True)
        
        print(f"✅ Recent Trades Status:")
        print(f"   Total Trade Files: {len(trade_files)}")
        
        # Check last 3 trades
        for i, trade_file in enumerate(trade_files[:3]):
            try:
                with open(trade_file, 'r') as f:
                    trade_data = json.load(f)
                
                signal = trade_data.get('signal', {})
                tx_result = trade_data.get('transaction_result', {})
                position_data = trade_data.get('position_data', {})
                
                timestamp = datetime.fromisoformat(trade_data['timestamp'].replace('Z', '+00:00'))
                
                print(f"   Trade {i+1}: {signal.get('action', 'N/A')} {position_data.get('position_size_sol', 0):.4f} SOL")
                print(f"     Time: {timestamp.strftime('%H:%M:%S')}")
                print(f"     Status: {'✅ Executed' if tx_result.get('success') else '❌ Failed'}")
                print(f"     Signature: {tx_result.get('signature', 'N/A')[:16]}..." if tx_result.get('signature') else "     No signature")
                
            except Exception as e:
                print(f"   Error reading trade file {trade_file}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking trades: {e}")
        return False

def check_dashboard_data_sources():
    """Check if dashboard data sources exist."""
    print("✅ Dashboard Data Sources:")
    
    sources = {
        'Enhanced Live Trading': 'output/enhanced_live_trading/',
        'Live Production': 'output/live_production/',
        'Paper Trading': 'output/paper_trading/',
        'Wallet Data': 'output/wallet/'
    }
    
    for name, path in sources.items():
        if os.path.exists(path):
            files = len([f for f in os.listdir(path) if os.path.isfile(os.path.join(path, f))])
            print(f"   {name}: ✅ Available ({files} files)")
        else:
            print(f"   {name}: ❌ Not found")

def main():
    """Main status check function."""
    print("🔍 Enhanced Live Trading System Status Check")
    print("=" * 50)
    print()
    
    # Check if process is running
    process_running = check_live_trading_process()
    print()
    
    # Check latest metrics
    metrics_available = check_latest_metrics()
    print()
    
    # Check recent trades
    trades_available = check_recent_trades()
    print()
    
    # Check dashboard data sources
    check_dashboard_data_sources()
    print()
    
    # Overall status
    print("🎯 Overall Status:")
    if process_running and metrics_available and trades_available:
        print("✅ Enhanced Live Trading System is FULLY OPERATIONAL")
        print("✅ Dashboard should display real-time data")
        print("✅ All components are working correctly")
    else:
        print("⚠️ Some components may not be working correctly")
        if not process_running:
            print("   - Live trading process not running")
        if not metrics_available:
            print("   - Metrics not being generated")
        if not trades_available:
            print("   - Trades not being recorded")
    
    print()
    print("📊 Dashboard URLs:")
    print("   Enhanced Dashboard: http://localhost:8503")
    print("   Production Dashboard: http://localhost:8501")

if __name__ == "__main__":
    main()
