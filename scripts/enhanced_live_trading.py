#!/usr/bin/env python3
"""
Enhanced Live Trading System with Fixed Transaction Issues
Integrates all enhanced components for robust, future-proofed trading.
"""

import asyncio
import logging
import json
import os
import sys
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Enhanced components
from core.transaction.enhanced_tx_builder import EnhancedTxBuilder
from core.transaction.enhanced_tx_executor import EnhancedTxExecutor
from core.wallet.secure_wallet_manager import SecureWalletManager
from core.risk.production_position_sizer import ProductionPositionSizer
from core.risk.position_flattener import PositionFlattener
from core.notifications.telegram_notifier import TelegramNotifier

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/enhanced_live_trading.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedLiveTradingSystem:
    """
    Enhanced live trading system with fixed transaction issues and comprehensive monitoring.
    Future-proofed with robust error handling and multiple fallback mechanisms.
    """

    def __init__(self, config_path: str = "config/live_production.yaml"):
        """Initialize the enhanced live trading system."""
        self.config_path = config_path
        self.config = self._load_config()

        # Core components
        self.wallet_manager = None
        self.tx_builder = None
        self.tx_executor = None
        self.position_sizer = None
        self.position_flattener = None
        self.telegram_notifier = None

        # State tracking
        self.session_start = datetime.now()
        self.trades_executed = 0
        self.total_pnl = 0.0
        self.is_running = False

        # Performance metrics
        self.metrics = {
            'cycles_completed': 0,
            'cycles_successful': 0,
            'trades_attempted': 0,
            'trades_executed': 0,
            'trades_rejected': 0,
            'total_fees': 0.0,
            'total_pnl': 0.0,
            'errors': []
        }

        logger.info("Enhanced Live Trading System initialized")

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            import yaml
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)

            # Substitute environment variables
            return self._substitute_env_vars(config)

        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            # Return default configuration
            return {
                'wallet': {'active_trading_pct': 0.5},
                'trading': {'update_interval': 30, 'max_trades_per_day': 10},
                'risk_management': {'max_portfolio_exposure': 0.5}
            }

    def _substitute_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Substitute environment variables in configuration."""
        def substitute_recursive(obj):
            if isinstance(obj, dict):
                return {k: substitute_recursive(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [substitute_recursive(item) for item in obj]
            elif isinstance(obj, str) and obj.startswith('${') and obj.endswith('}'):
                env_var = obj[2:-1]
                return os.getenv(env_var, obj)
            else:
                return obj

        return substitute_recursive(config)

    async def initialize_components(self) -> bool:
        """Initialize all trading system components."""
        try:
            logger.info("Initializing enhanced trading components...")

            # Initialize wallet manager
            self.wallet_manager = SecureWalletManager()

            # Load keypair
            keypair_path = os.getenv('KEYPAIR_PATH')
            wallet_address = os.getenv('WALLET_ADDRESS')

            if not keypair_path or not wallet_address:
                logger.error("KEYPAIR_PATH and WALLET_ADDRESS must be set")
                return False

            success, msg = self.wallet_manager.load_keypair('trading', keypair_path, wallet_address)
            if not success:
                logger.error(f"Failed to load keypair: {msg}")
                return False

            logger.info(f"Wallet loaded: {self.wallet_manager.get_active_address()}")

            # Initialize transaction builder
            self.tx_builder = EnhancedTxBuilder(wallet_address, keypair_path)

            # Test builder status
            builder_status = self.tx_builder.get_status()
            logger.info(f"Transaction builder status: {builder_status}")

            if not builder_status['keypair_loaded']:
                logger.error("Transaction builder failed to load keypair")
                return False

            # Initialize transaction executor
            rpc_urls = [
                os.getenv('HELIUS_RPC_URL'),
                'https://api.mainnet-beta.solana.com'
            ]
            rpc_urls = [url for url in rpc_urls if url]  # Remove None values

            self.tx_executor = EnhancedTxExecutor(rpc_urls)

            # Initialize position sizer
            self.position_sizer = ProductionPositionSizer(self.config)

            # Initialize Telegram notifier
            self.telegram_notifier = TelegramNotifier()

            # Test Telegram connection
            if self.telegram_notifier.enabled:
                logger.info("Testing Telegram connection...")
                telegram_test = await self.telegram_notifier.test_connection()
                if telegram_test:
                    logger.info("✅ Telegram notifications enabled")
                else:
                    logger.warning("⚠️ Telegram test failed - notifications may not work")
            else:
                logger.info("ℹ️ Telegram notifications disabled (no credentials)")

            # Initialize position flattener
            self.position_flattener = PositionFlattener(
                self.wallet_manager,
                self.tx_builder,
                self.tx_executor,
                self.telegram_notifier
            )
            logger.info("✅ Position flattener initialized")

            # Update position sizer with current wallet state
            await self._update_wallet_state()

            logger.info("All components initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Error initializing components: {e}")
            return False

    async def _update_wallet_state(self):
        """Update wallet state for position sizing."""
        try:
            # Load wallet balance from file or get from RPC
            wallet_file = "output/wallet/wallet_balance.json"
            if os.path.exists(wallet_file):
                with open(wallet_file, 'r') as f:
                    wallet_data = json.load(f)

                wallet_balance = wallet_data.get('wallet_balance', {}).get('trading_wallet', 5.0)
            else:
                # Default balance for testing
                wallet_balance = 5.0

            # Update position sizer
            self.position_sizer.update_wallet_state(wallet_balance, 0.0, 180.0)

            logger.info(f"Wallet state updated: {wallet_balance:.4f} SOL")

        except Exception as e:
            logger.error(f"Error updating wallet state: {e}")

    async def generate_trading_signal(self) -> Dict[str, Any]:
        """Generate a trading signal for testing."""
        # Simple signal generation for testing
        import random

        actions = ['BUY', 'SELL']
        markets = ['SOL-USDC']

        signal = {
            'action': random.choice(actions),
            'market': random.choice(markets),
            'price': 180.0 + random.uniform(-5, 5),
            'size': 0.01 + random.uniform(0, 0.02),  # Small test sizes
            'confidence': 0.7 + random.uniform(0, 0.3),
            'timestamp': datetime.now().isoformat(),
            'strategy': 'enhanced_test',
            'metadata': {
                'signal_strength': random.uniform(0.6, 0.9),
                'market_regime': 'ranging',
                'volatility': random.uniform(0.2, 0.5)
            }
        }

        return signal

    async def execute_trading_cycle(self) -> Dict[str, Any]:
        """Execute a single trading cycle."""
        cycle_start = datetime.now()
        cycle_data = {
            'cycle_number': self.metrics['cycles_completed'] + 1,
            'timestamp': cycle_start.isoformat(),
            'status': 'started'
        }

        try:
            logger.info(f"Starting trading cycle {cycle_data['cycle_number']}")

            # Check daily limits
            max_trades = self.config.get('trading', {}).get('max_trades_per_day', 10)
            if self.trades_executed >= max_trades:
                cycle_data['status'] = 'skipped'
                cycle_data['reason'] = 'Daily trade limit reached'
                return cycle_data

            # Update wallet state
            await self._update_wallet_state()

            # Generate trading signal
            signal = await self.generate_trading_signal()
            logger.info(f"Generated signal: {signal['action']} {signal['size']:.4f} {signal['market']}")

            # Calculate position size
            position_data = self.position_sizer.calculate_position_size(
                signal_strength=signal['metadata']['signal_strength'],
                strategy=signal['strategy'],
                market_regime=signal['metadata']['market_regime'],
                volatility=signal['metadata']['volatility']
            )

            if position_data.get('rejected', False):
                rejection_reason = position_data.get('rejection_reason', 'Unknown reason')
                logger.warning(f"Position rejected: {rejection_reason}")

                # Send Telegram notification for rejected trade
                if self.telegram_notifier and self.telegram_notifier.enabled:
                    await self.telegram_notifier.notify_trade_rejected(signal, rejection_reason)

                cycle_data['status'] = 'rejected'
                cycle_data['rejection_reason'] = rejection_reason
                self.metrics['trades_rejected'] += 1
                return cycle_data

            logger.info(f"Position size: {position_data['position_size_sol']:.4f} SOL "
                       f"(${position_data['position_size_usd']:.2f})")

            # Update signal with position size
            signal['size'] = position_data['position_size_sol']
            signal['position_data'] = position_data

            # Build transaction
            logger.info("Building transaction...")
            transaction = await self.tx_builder.build_transaction_from_signal(signal)

            if not transaction:
                logger.error("Failed to build transaction")
                cycle_data['status'] = 'failed'
                cycle_data['error'] = 'Transaction building failed'
                self.metrics['errors'].append('Transaction building failed')
                return cycle_data

            logger.info("Transaction built successfully")

            # Simulate transaction first
            logger.info("Simulating transaction...")
            simulation = await self.tx_executor.simulate_transaction(transaction)

            if not simulation.get('success', False):
                logger.warning(f"Transaction simulation failed: {simulation.get('error')}")
                # Continue anyway for testing, but log the issue
            else:
                logger.info("Transaction simulation successful")

            # Execute transaction
            logger.info("Executing transaction...")
            self.metrics['trades_attempted'] += 1

            result = await self.tx_executor.send_transaction(transaction)

            if result.get('success', False):
                logger.info(f"Transaction executed successfully: {result.get('signature')}")

                self.trades_executed += 1
                self.metrics['trades_executed'] += 1

                # Record trade data
                trade_data = {
                    'signal': signal,
                    'position_data': position_data,
                    'transaction_result': result,
                    'timestamp': datetime.now().isoformat()
                }

                # Save trade data
                await self._save_trade_data(trade_data)

                # Send Telegram notification for successful trade
                if self.telegram_notifier and self.telegram_notifier.enabled:
                    await self.telegram_notifier.notify_trade_executed(trade_data)

                cycle_data['status'] = 'completed'
                cycle_data['trade_executed'] = True
                cycle_data['signature'] = result.get('signature')
                cycle_data['execution_time'] = result.get('execution_time')

            else:
                logger.error(f"Transaction execution failed: {result.get('error')}")
                cycle_data['status'] = 'failed'
                cycle_data['error'] = result.get('error')
                cycle_data['trade_executed'] = False
                self.metrics['errors'].append(result.get('error', 'Unknown execution error'))

            return cycle_data

        except Exception as e:
            logger.error(f"Error in trading cycle: {e}")
            cycle_data['status'] = 'error'
            cycle_data['error'] = str(e)
            self.metrics['errors'].append(str(e))
            return cycle_data

        finally:
            # Update metrics
            self.metrics['cycles_completed'] += 1
            if cycle_data.get('status') == 'completed':
                self.metrics['cycles_successful'] += 1

            # Save cycle data
            await self._save_cycle_data(cycle_data)

    async def _save_trade_data(self, trade_data: Dict[str, Any]):
        """Save individual trade data."""
        try:
            os.makedirs("output/enhanced_live_trading/trades", exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            trade_file = f"output/enhanced_live_trading/trades/trade_{timestamp}.json"

            with open(trade_file, 'w') as f:
                json.dump(trade_data, f, indent=2, default=str)

            logger.debug(f"Trade data saved: {trade_file}")

        except Exception as e:
            logger.error(f"Error saving trade data: {e}")

    async def _save_cycle_data(self, cycle_data: Dict[str, Any]):
        """Save cycle data for analysis."""
        try:
            os.makedirs("output/enhanced_live_trading/cycles", exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            cycle_file = f"output/enhanced_live_trading/cycles/cycle_{cycle_data['cycle_number']:04d}_{timestamp}.json"

            with open(cycle_file, 'w') as f:
                json.dump(cycle_data, f, indent=2, default=str)

            # Save latest metrics
            metrics_file = "output/enhanced_live_trading/latest_metrics.json"
            metrics_data = {
                'metrics': self.metrics,
                'session_start': self.session_start.isoformat(),
                'session_duration_minutes': (datetime.now() - self.session_start).total_seconds() / 60,
                'executor_metrics': self.tx_executor.get_metrics() if self.tx_executor else {},
                'timestamp': datetime.now().isoformat()
            }

            with open(metrics_file, 'w') as f:
                json.dump(metrics_data, f, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error saving cycle data: {e}")

    async def run_trading_session(self, duration_hours: Optional[float] = None):
        """Run the enhanced live trading session."""
        logger.info("🚀 Starting Enhanced Live Trading Session")

        # Initialize components
        if not await self.initialize_components():
            logger.error("Failed to initialize components")
            return False

        # Calculate end time
        end_time = None
        if duration_hours:
            end_time = datetime.now() + timedelta(hours=duration_hours)
            logger.info(f"Session will run for {duration_hours} hours until {end_time}")

        # Send session start notification with wallet balance
        if self.telegram_notifier and self.telegram_notifier.enabled:
            current_balance = await self.wallet_manager.get_balance()
            await self.telegram_notifier.notify_session_started(duration_hours, current_balance)

        self.is_running = True
        cycle_count = 0

        try:
            while self.is_running:
                # Check if we should stop
                if end_time and datetime.now() >= end_time:
                    logger.info("Session duration reached")
                    break

                # Execute trading cycle
                cycle_count += 1
                cycle_result = await self.execute_trading_cycle()

                # Log cycle results
                status = cycle_result.get('status', 'unknown')
                logger.info(f"Cycle {cycle_count} {status}")

                if status == 'completed':
                    logger.info(f"✅ Trade executed: {cycle_result.get('signature', 'N/A')}")
                elif status == 'rejected':
                    logger.info(f"⚠️ Trade rejected: {cycle_result.get('rejection_reason', 'Unknown')}")
                elif status == 'failed':
                    logger.warning(f"❌ Cycle failed: {cycle_result.get('error', 'Unknown')}")

                # Wait before next cycle
                cycle_interval = self.config.get('trading', {}).get('update_interval', 30)
                await asyncio.sleep(cycle_interval)

        except KeyboardInterrupt:
            logger.info("Trading session stopped by user")
        except Exception as e:
            logger.error(f"Error in trading session: {e}")
        finally:
            self.is_running = False

            # CRITICAL: Execute position flattening before cleanup
            logger.info("🔄 Executing position flattening before session end...")
            try:
                if self.position_flattener:
                    # Get current price for flattening
                    current_price = 180.0  # Default fallback
                    try:
                        # Try to get current market price
                        # This could be enhanced with real price feed
                        current_price = 180.0  # For now, use conservative estimate
                    except:
                        pass

                    # Execute position flattening
                    flattening_result = await self.position_flattener.execute_position_flattening(current_price)

                    if flattening_result['flattening_executed']:
                        logger.info("✅ Position flattening completed successfully")

                        # Send Telegram notification about flattening
                        if self.telegram_notifier and self.telegram_notifier.enabled:
                            await self.telegram_notifier.send_message(
                                "🛡️ *SESSION END PROTECTION*\n\n"
                                "Position flattening executed to prevent overnight risk.\n"
                                "All positions have been closed safely! ✅"
                            )
                    else:
                        logger.info(f"ℹ️ Position flattening: {flattening_result['reason']}")

            except Exception as e:
                logger.error(f"❌ Error during position flattening: {e}")

                # Send emergency alert
                if self.telegram_notifier and self.telegram_notifier.enabled:
                    await self.telegram_notifier.notify_error(
                        f"CRITICAL: Position flattening failed - {e}",
                        "Emergency Alert"
                    )

            # Cleanup
            if self.tx_builder:
                await self.tx_builder.close()
            if self.tx_executor:
                await self.tx_executor.close()

            # Final summary
            session_duration = (datetime.now() - self.session_start).total_seconds() / 60
            logger.info(f"Session completed: {cycle_count} cycles, {self.trades_executed} trades executed")
            logger.info(f"Session duration: {session_duration:.1f} minutes")
            logger.info(f"Success rate: {self.metrics['cycles_successful']}/{self.metrics['cycles_completed']}")

            # Send session end notification with PnL data
            if self.telegram_notifier and self.telegram_notifier.enabled:
                session_metrics = {
                    'cycles_completed': self.metrics['cycles_completed'],
                    'trades_executed': self.metrics['trades_executed'],
                    'trades_rejected': self.metrics['trades_rejected'],
                    'session_duration_minutes': session_duration
                }

                # Get final balance and calculate average price
                try:
                    final_balance = await self.wallet_manager.get_balance()
                    # Calculate average price from executed trades (if any)
                    avg_price = 180.0  # Default fallback
                    if hasattr(self, 'executed_trades') and self.executed_trades:
                        prices = [trade.get('price', 180.0) for trade in self.executed_trades]
                        avg_price = sum(prices) / len(prices) if prices else 180.0

                    await self.telegram_notifier.notify_session_ended(session_metrics, final_balance, avg_price)
                except Exception as e:
                    logger.error(f"Error getting final balance for Telegram notification: {e}")
                    await self.telegram_notifier.notify_session_ended(session_metrics)

            # Close Telegram notifier
            if self.telegram_notifier:
                await self.telegram_notifier.close()

        return True


async def main():
    """Main function for enhanced live trading."""
    import argparse

    parser = argparse.ArgumentParser(description="Enhanced Live Trading with Fixed Transaction Issues")
    parser.add_argument("--config", default="config/live_production.yaml", help="Configuration file")
    parser.add_argument("--duration", type=float, default=0.25, help="Trading duration in hours")
    parser.add_argument("--test-mode", action="store_true", help="Run in test mode")

    args = parser.parse_args()

    # Create trading system
    trading_system = EnhancedLiveTradingSystem(args.config)

    # Run trading session
    success = await trading_system.run_trading_session(args.duration)

    if success:
        logger.info("✅ Enhanced live trading session completed successfully")
        return 0
    else:
        logger.error("❌ Enhanced live trading session failed")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
