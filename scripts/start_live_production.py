#!/usr/bin/env python3
"""
Enhanced Live Production Trading Script
Implements 0.5 wallet strategy with optimized position sizing and comprehensive monitoring.
"""

import asyncio
import logging
import json
import yaml
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from core.risk.production_position_sizer import ProductionPositionSizer
from phase_4_deployment.start_live_trading import run_live_trading

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/live_production.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionTradingSystem:
    """Enhanced trading system for live production with 0.5 wallet strategy."""

    def __init__(self, config_path: str = "config/live_production.yaml"):
        """Initialize the production trading system."""
        self.config_path = config_path
        self.config = self._load_config()

        # Initialize components
        self.position_sizer = ProductionPositionSizer(self.config)
        self.trading_system = None

        # State tracking
        self.wallet_balance = 0.0
        self.active_capital = 0.0
        self.current_exposure = 0.0
        self.sol_price = 180.0
        self.trades_today = 0
        self.daily_pnl = 0.0
        self.session_start_time = datetime.now()

        # Performance tracking
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl_sol': 0.0,
            'total_pnl_usd': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'win_rate': 0.0
        }

        logger.info("Production Trading System initialized with 0.5 wallet strategy")

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)

            # Substitute environment variables
            config = self._substitute_env_vars(config)

            logger.info(f"Configuration loaded from {self.config_path}")
            return config

        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise

    def _substitute_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Substitute environment variables in configuration."""
        def substitute_recursive(obj):
            if isinstance(obj, dict):
                return {k: substitute_recursive(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [substitute_recursive(item) for item in obj]
            elif isinstance(obj, str) and obj.startswith('${') and obj.endswith('}'):
                env_var = obj[2:-1]
                return os.getenv(env_var, obj)
            else:
                return obj

        return substitute_recursive(config)

    async def initialize_trading_system(self):
        """Initialize the trading system."""
        try:
            # Update wallet state
            await self._update_wallet_state()

            logger.info("Trading system initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize trading system: {e}")
            return False

    async def _update_wallet_state(self):
        """Update current wallet state for position sizing."""
        try:
            # Load wallet balance
            wallet_file = "output/wallet/wallet_balance.json"
            if os.path.exists(wallet_file):
                with open(wallet_file, 'r') as f:
                    wallet_data = json.load(f)

                self.wallet_balance = wallet_data.get('wallet_balance', {}).get('trading_wallet', 0.0)
                self.active_capital = self.wallet_balance * self.config.get('wallet', {}).get('active_trading_pct', 0.5)

                logger.info(f"Wallet state updated: {self.wallet_balance:.4f} SOL total, "
                           f"{self.active_capital:.4f} SOL active capital")

            # Update position sizer
            self.position_sizer.update_wallet_state(
                self.wallet_balance,
                self.current_exposure,
                self.sol_price
            )

        except Exception as e:
            logger.error(f"Error updating wallet state: {e}")

    async def execute_trading_cycle(self) -> Dict[str, Any]:
        """Execute a single trading cycle with enhanced position sizing."""
        cycle_start = datetime.now()
        cycle_data = {
            'cycle_number': self.trades_today + 1,
            'timestamp': cycle_start.isoformat(),
            'wallet_balance': self.wallet_balance,
            'active_capital': self.active_capital,
            'current_exposure': self.current_exposure,
            'sol_price': self.sol_price
        }

        try:
            # Check daily limits
            max_trades_per_day = self.config.get('trading', {}).get('max_trades_per_day', 10)
            if self.trades_today >= max_trades_per_day:
                cycle_data['status'] = 'skipped'
                cycle_data['reason'] = 'Daily trade limit reached'
                return cycle_data

            # Update wallet state
            await self._update_wallet_state()

            # Execute trading cycle using run_live_trading function
            try:
                # Run a single cycle of live trading
                logger.info("Executing live trading cycle...")

                # Create a task for the trading cycle with timeout
                trading_task = asyncio.create_task(run_live_trading())

                # Wait for a short period (1 cycle)
                await asyncio.sleep(60)  # 1 minute cycle

                # Cancel the task after one cycle
                trading_task.cancel()

                try:
                    await trading_task
                except asyncio.CancelledError:
                    pass

                cycle_data['status'] = 'completed'
                cycle_data['trades_executed'] = []  # Will be populated from output files

            except Exception as e:
                cycle_data['status'] = 'error'
                cycle_data['error'] = f'Trading cycle error: {str(e)}'

            # Update performance metrics
            self._update_performance_metrics(cycle_data)

            # Save cycle data
            await self._save_cycle_data(cycle_data)

            return cycle_data

        except Exception as e:
            logger.error(f"Error in trading cycle: {e}")
            cycle_data['status'] = 'error'
            cycle_data['error'] = str(e)
            return cycle_data

    async def _process_trade_result(self, trade: Dict[str, Any]):
        """Process individual trade results."""
        try:
            if trade.get('status') == 'executed':
                self.trades_today += 1

                # Update exposure
                trade_size = trade.get('sol_amount', 0.0)
                if trade.get('type') == 'buy':
                    self.current_exposure += trade_size
                elif trade.get('type') == 'sell':
                    self.current_exposure -= trade_size

                # Update P&L (simplified)
                trade_pnl = trade.get('pnl_usd', 0.0)
                self.daily_pnl += trade_pnl

                logger.info(f"Trade processed: {trade.get('type')} {trade_size:.4f} SOL, "
                           f"P&L: ${trade_pnl:.2f}")

        except Exception as e:
            logger.error(f"Error processing trade result: {e}")

    def _update_performance_metrics(self, cycle_data: Dict[str, Any]):
        """Update performance tracking metrics."""
        try:
            # Update basic metrics
            if cycle_data.get('status') == 'completed':
                trades = cycle_data.get('trades_executed', [])
                for trade in trades:
                    if trade.get('status') == 'executed':
                        self.performance_metrics['total_trades'] += 1

                        pnl = trade.get('pnl_usd', 0.0)
                        if pnl > 0:
                            self.performance_metrics['winning_trades'] += 1

                        self.performance_metrics['total_pnl_usd'] += pnl

            # Calculate derived metrics
            if self.performance_metrics['total_trades'] > 0:
                self.performance_metrics['win_rate'] = (
                    self.performance_metrics['winning_trades'] /
                    self.performance_metrics['total_trades']
                )

        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")

    async def _save_cycle_data(self, cycle_data: Dict[str, Any]):
        """Save cycle data for dashboard and analysis."""
        try:
            # Ensure output directory exists
            os.makedirs("output/live_production/cycles", exist_ok=True)
            os.makedirs("output/live_production/dashboard", exist_ok=True)

            # Save individual cycle data
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            cycle_file = f"output/live_production/cycles/cycle_{cycle_data['cycle_number']:04d}_{timestamp}.json"

            with open(cycle_file, 'w') as f:
                json.dump(cycle_data, f, indent=2, default=str)

            # Save latest cycle for dashboard
            latest_file = "output/live_production/dashboard/latest_cycle.json"
            with open(latest_file, 'w') as f:
                json.dump(cycle_data, f, indent=2, default=str)

            # Save performance metrics
            metrics_file = "output/live_production/dashboard/performance_metrics.json"
            metrics_data = {
                'performance_metrics': self.performance_metrics,
                'portfolio_status': self.position_sizer.get_portfolio_status(),
                'session_stats': {
                    'session_start': self.session_start_time.isoformat(),
                    'trades_today': self.trades_today,
                    'daily_pnl_usd': self.daily_pnl,
                    'active_capital_usd': self.active_capital * self.sol_price,
                    'current_exposure_pct': (self.current_exposure / self.wallet_balance) if self.wallet_balance > 0 else 0
                },
                'timestamp': datetime.now().isoformat()
            }

            with open(metrics_file, 'w') as f:
                json.dump(metrics_data, f, indent=2, default=str)

            logger.debug(f"Cycle data saved: {cycle_file}")

        except Exception as e:
            logger.error(f"Error saving cycle data: {e}")

    async def run_production_trading(self, duration_hours: Optional[float] = None):
        """Run live production trading with the 0.5 wallet strategy."""
        logger.info("🚀 Starting Live Production Trading with 0.5 Wallet Strategy")

        # Initialize trading system
        if not await self.initialize_trading_system():
            logger.error("Failed to initialize trading system")
            return False

        # Calculate end time if duration specified
        end_time = None
        if duration_hours:
            end_time = datetime.now() + timedelta(hours=duration_hours)
            logger.info(f"Trading session will run for {duration_hours} hours until {end_time}")

        # Main trading loop
        cycle_count = 0
        try:
            while True:
                # Check if we should stop
                if end_time and datetime.now() >= end_time:
                    logger.info("Trading session duration reached")
                    break

                # Execute trading cycle
                cycle_count += 1
                logger.info(f"Starting trading cycle {cycle_count}")

                cycle_result = await self.execute_trading_cycle()

                # Log cycle results
                if cycle_result.get('status') == 'completed':
                    trades_executed = len(cycle_result.get('trades_executed', []))
                    logger.info(f"Cycle {cycle_count} completed: {trades_executed} trades executed")
                elif cycle_result.get('status') == 'skipped':
                    logger.info(f"Cycle {cycle_count} skipped: {cycle_result.get('reason', 'Unknown')}")
                else:
                    logger.warning(f"Cycle {cycle_count} failed: {cycle_result.get('error', 'Unknown error')}")

                # Wait before next cycle
                cycle_interval = self.config.get('trading', {}).get('update_interval', 30)
                await asyncio.sleep(cycle_interval)

        except KeyboardInterrupt:
            logger.info("Trading stopped by user")
        except Exception as e:
            logger.error(f"Error in trading loop: {e}")

        # Final summary
        logger.info(f"Trading session completed: {cycle_count} cycles, {self.trades_today} trades executed")
        logger.info(f"Session P&L: ${self.daily_pnl:.2f} USD")

        return True


async def main():
    """Main function for production trading."""
    import argparse

    parser = argparse.ArgumentParser(description="Live Production Trading with 0.5 Wallet Strategy")
    parser.add_argument("--config", default="config/live_production.yaml", help="Configuration file path")
    parser.add_argument("--duration", type=float, help="Trading duration in hours")
    parser.add_argument("--test-mode", action="store_true", help="Run in test mode (shorter cycles)")

    args = parser.parse_args()

    # Create production trading system
    trading_system = ProductionTradingSystem(args.config)

    # Adjust for test mode
    if args.test_mode:
        logger.info("Running in test mode")
        duration = args.duration or 0.5  # 30 minutes default for test
    else:
        duration = args.duration

    # Start trading
    success = await trading_system.run_production_trading(duration)

    if success:
        logger.info("✅ Production trading completed successfully")
        return 0
    else:
        logger.error("❌ Production trading failed")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
