#!/usr/bin/env python3
"""
Comprehensive Test Script for Transaction Fixes
Tests all enhanced components to ensure transaction issues are resolved.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from core.transaction.enhanced_tx_builder import EnhancedTxBuilder
from core.transaction.enhanced_tx_executor import EnhancedTxExecutor
from core.wallet.secure_wallet_manager import SecureWalletManager

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TransactionFixValidator:
    """Comprehensive validator for transaction fixes."""

    def __init__(self):
        """Initialize the validator."""
        self.wallet_address = os.getenv('WALLET_ADDRESS')
        self.keypair_path = os.getenv('KEYPAIR_PATH')
        self.rpc_url = os.getenv('HELIUS_RPC_URL')

        self.test_results = {}

    async def test_wallet_manager(self) -> bool:
        """Test the secure wallet manager."""
        logger.info("🔐 Testing Secure Wallet Manager...")

        try:
            # Create wallet manager
            wallet_manager = SecureWalletManager()

            # Test status
            status = wallet_manager.get_status()
            logger.info(f"Wallet manager status: {status}")

            if not status['solders_available']:
                logger.error("❌ Solders not available")
                return False

            # Test keypair validation
            if self.keypair_path and os.path.exists(self.keypair_path):
                is_valid, msg = wallet_manager.validate_keypair_file(self.keypair_path)
                logger.info(f"Keypair validation: {is_valid}, {msg}")

                if not is_valid:
                    logger.error(f"❌ Keypair validation failed: {msg}")
                    return False

                # Test keypair loading
                success, load_msg = wallet_manager.load_keypair('test', self.keypair_path, self.wallet_address)
                logger.info(f"Keypair loading: {success}, {load_msg}")

                if not success:
                    logger.error(f"❌ Keypair loading failed: {load_msg}")
                    return False

                # Test active keypair
                active_address = wallet_manager.get_active_address()
                logger.info(f"Active address: {active_address}")

                if active_address != self.wallet_address:
                    logger.error(f"❌ Address mismatch: {active_address} != {self.wallet_address}")
                    return False

                logger.info("✅ Wallet manager tests passed")
                return True
            else:
                logger.error(f"❌ Keypair file not found: {self.keypair_path}")
                return False

        except Exception as e:
            logger.error(f"❌ Wallet manager test failed: {e}")
            return False

    async def test_tx_builder(self) -> bool:
        """Test the enhanced transaction builder."""
        logger.info("🔨 Testing Enhanced Transaction Builder...")

        try:
            # Create transaction builder
            tx_builder = EnhancedTxBuilder(self.wallet_address, self.keypair_path, self.rpc_url)

            # Test status
            status = tx_builder.get_status()
            logger.info(f"Transaction builder status: {status}")

            if not status['keypair_loaded']:
                logger.error("❌ Keypair not loaded in transaction builder")
                return False

            if not status['solders_available']:
                logger.error("❌ Solders not available in transaction builder")
                return False

            # Test blockhash retrieval
            logger.info("Testing blockhash retrieval...")
            blockhash = await tx_builder.get_recent_blockhash()
            if blockhash:
                logger.info(f"✅ Blockhash retrieved: {blockhash[:16]}...")
            else:
                logger.error("❌ Failed to retrieve blockhash")
                return False

            # Test Jupiter quote (may fail due to network/API, but should not crash)
            logger.info("Testing Jupiter quote...")
            try:
                quote = await tx_builder.get_jupiter_quote(
                    "So11111111111111111111111111111111111111112",  # SOL
                    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
                    1000000000,  # 1 SOL in lamports
                    50  # 0.5% slippage
                )
                if quote:
                    logger.info("✅ Jupiter quote retrieved successfully")
                else:
                    logger.warning("⚠️ Jupiter quote failed (may be expected)")
            except Exception as e:
                logger.warning(f"⚠️ Jupiter quote error (may be expected): {e}")

            # Test simple transfer building
            logger.info("Testing simple transfer building...")
            transfer_tx = await tx_builder.build_simple_transfer(self.wallet_address, 1000)
            if transfer_tx:
                logger.info(f"✅ Simple transfer built: {len(transfer_tx)} characters")
            else:
                logger.error("❌ Failed to build simple transfer")
                return False

            # Test signal-based transaction building
            logger.info("Testing signal-based transaction building...")
            test_signal = {
                "action": "BUY",
                "market": "SOL-USDC",
                "price": 180.0,
                "size": 0.001,  # Very small test size
                "confidence": 0.8
            }

            signal_tx = await tx_builder.build_transaction_from_signal(test_signal)
            if signal_tx:
                logger.info(f"✅ Signal-based transaction built: {len(signal_tx)} characters")
            else:
                logger.warning("⚠️ Signal-based transaction failed (may fall back to transfer)")

            await tx_builder.close()
            logger.info("✅ Transaction builder tests passed")
            return True

        except Exception as e:
            logger.error(f"❌ Transaction builder test failed: {e}")
            return False

    async def test_tx_executor(self) -> bool:
        """Test the enhanced transaction executor."""
        logger.info("⚡ Testing Enhanced Transaction Executor...")

        try:
            # Create transaction executor
            rpc_urls = [self.rpc_url, 'https://api.mainnet-beta.solana.com']
            rpc_urls = [url for url in rpc_urls if url]

            tx_executor = EnhancedTxExecutor(rpc_urls)

            # Test with a dummy transaction (will fail but tests the flow)
            logger.info("Testing transaction execution flow...")
            dummy_tx = "dGVzdCB0cmFuc2FjdGlvbg=="  # "test transaction" in base64

            result = await tx_executor.send_transaction(dummy_tx)
            logger.info(f"Dummy transaction result: {result}")

            # This should fail, but we're testing the error handling
            if not result.get('success', False):
                logger.info("✅ Transaction executor properly handled invalid transaction")
            else:
                logger.warning("⚠️ Unexpected success with dummy transaction")

            # Test simulation
            logger.info("Testing transaction simulation...")
            sim_result = await tx_executor.simulate_transaction(dummy_tx)
            logger.info(f"Simulation result: {sim_result}")

            # Get metrics
            metrics = tx_executor.get_metrics()
            logger.info(f"Executor metrics: {metrics}")

            await tx_executor.close()
            logger.info("✅ Transaction executor tests passed")
            return True

        except Exception as e:
            logger.error(f"❌ Transaction executor test failed: {e}")
            return False

    async def test_integration(self) -> bool:
        """Test integration of all components."""
        logger.info("🔗 Testing Component Integration...")

        try:
            # Create all components
            wallet_manager = SecureWalletManager()
            tx_builder = EnhancedTxBuilder(self.wallet_address, self.keypair_path, self.rpc_url)
            tx_executor = EnhancedTxExecutor([self.rpc_url])

            # Load keypair
            success, msg = wallet_manager.load_keypair('test', self.keypair_path, self.wallet_address)
            if not success:
                logger.error(f"❌ Integration test: keypair loading failed: {msg}")
                return False

            # Build a transaction
            test_signal = {
                "action": "BUY",
                "market": "SOL-USDC",
                "price": 180.0,
                "size": 0.001,
                "confidence": 0.8
            }

            transaction = await tx_builder.build_transaction_from_signal(test_signal)
            if not transaction:
                logger.error("❌ Integration test: transaction building failed")
                return False

            # Simulate the transaction
            sim_result = await tx_executor.simulate_transaction(transaction)
            logger.info(f"Integration simulation result: {sim_result}")

            # Note: We don't actually send the transaction to avoid spending real funds
            logger.info("✅ Integration test passed (transaction not sent to preserve funds)")

            # Cleanup
            await tx_builder.close()
            await tx_executor.close()

            return True

        except Exception as e:
            logger.error(f"❌ Integration test failed: {e}")
            return False

    async def run_all_tests(self) -> Dict[str, bool]:
        """Run all validation tests."""
        logger.info("🧪 Starting Comprehensive Transaction Fix Validation")
        logger.info("=" * 60)

        # Check prerequisites
        if not self.wallet_address:
            logger.error("❌ WALLET_ADDRESS not set")
            return {'prerequisites': False}

        if not self.keypair_path:
            logger.error("❌ KEYPAIR_PATH not set")
            return {'prerequisites': False}

        if not os.path.exists(self.keypair_path):
            logger.error(f"❌ Keypair file not found: {self.keypair_path}")
            return {'prerequisites': False}

        logger.info(f"✅ Prerequisites check passed")
        logger.info(f"   Wallet: {self.wallet_address}")
        logger.info(f"   Keypair: {self.keypair_path}")
        logger.info(f"   RPC: {self.rpc_url}")
        logger.info("")

        # Run tests
        tests = [
            ('wallet_manager', self.test_wallet_manager),
            ('tx_builder', self.test_tx_builder),
            ('tx_executor', self.test_tx_executor),
            ('integration', self.test_integration)
        ]

        results = {}

        for test_name, test_func in tests:
            logger.info(f"Running {test_name} test...")
            try:
                results[test_name] = await test_func()
            except Exception as e:
                logger.error(f"❌ {test_name} test crashed: {e}")
                results[test_name] = False

            logger.info("")

        # Summary
        logger.info("🎯 TEST SUMMARY")
        logger.info("=" * 40)

        all_passed = True
        for test_name, passed in results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            logger.info(f"{test_name:15}: {status}")
            if not passed:
                all_passed = False

        logger.info("")
        if all_passed:
            logger.info("🎉 ALL TESTS PASSED - Transaction issues are fixed!")
        else:
            logger.error("💥 SOME TESTS FAILED - Transaction issues remain")

        return results


async def main():
    """Main function for transaction fix validation."""
    validator = TransactionFixValidator()
    results = await validator.run_all_tests()

    # Return appropriate exit code
    all_passed = all(results.values())
    return 0 if all_passed else 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
