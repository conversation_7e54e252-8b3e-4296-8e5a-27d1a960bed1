#!/usr/bin/env python3
"""
Direct Keypair Creation from Base58 Private Key
"""

import os
import json
import base58
from solders.keypair import Keypair
from pathlib import Path

def create_keypair_direct():
    """Create keypair directly from Base58 private key."""
    
    # Your wallet configuration from .env
    wallet_address = "J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz"
    private_key_base58 = "sNniNt9pzZzF5Wvms73GHC5XLyVLJcZMZmoAHMedybyRHzLVLnRzmsafCdLtzhBN6T1szNvQWZgCXTuPVzgzY8L"
    keypair_path = "wallet/trading_wallet_keypair.json"
    
    print("🔐 Creating Keypair from Your Private Key")
    print("=" * 45)
    print(f"📍 Wallet Address: {wallet_address}")
    print(f"📁 Keypair Path: {keypair_path}")
    print(f"🔑 Private Key: {private_key_base58[:8]}...{private_key_base58[-8:]} (truncated)")
    print()
    
    try:
        # Decode Base58 private key
        print("🔄 Decoding Base58 private key...")
        private_key_bytes = base58.b58decode(private_key_base58)
        print(f"✅ Private key decoded: {len(private_key_bytes)} bytes")
        
        # Create keypair from private key
        print("🔄 Creating keypair...")
        keypair = Keypair.from_bytes(private_key_bytes)
        derived_pubkey = str(keypair.pubkey())
        print(f"✅ Keypair created successfully")
        
        # Verify the public key matches the configured wallet address
        print("🔄 Verifying public key...")
        print(f"   Derived pubkey: {derived_pubkey}")
        print(f"   Expected address: {wallet_address}")
        
        if derived_pubkey != wallet_address:
            print(f"❌ ERROR: Public key mismatch!")
            print("   This private key does not match the wallet address.")
            return False
        
        print(f"✅ Public key verification successful!")
        
        # Convert keypair to JSON array format
        print("🔄 Converting to JSON format...")
        keypair_bytes = bytes(keypair)
        keypair_array = list(keypair_bytes)
        print(f"✅ Keypair converted to array: {len(keypair_array)} elements")
        
        # Ensure output directory exists
        output_path = Path(keypair_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save keypair to file
        print(f"🔄 Saving keypair to {keypair_path}...")
        with open(keypair_path, 'w') as f:
            json.dump(keypair_array, f)
        
        # Set secure file permissions (owner read/write only)
        os.chmod(keypair_path, 0o600)
        print(f"✅ Keypair saved with secure permissions")
        
        # Verify the saved keypair
        print("🔄 Verifying saved keypair...")
        with open(keypair_path, 'r') as f:
            saved_keypair_data = json.load(f)
        
        # Test loading the saved keypair
        test_keypair = Keypair.from_bytes(bytes(saved_keypair_data))
        test_pubkey = str(test_keypair.pubkey())
        
        if test_pubkey != wallet_address:
            print("❌ ERROR: Saved keypair verification failed!")
            return False
        
        print("✅ Saved keypair verification successful!")
        
        # Check file permissions
        file_stat = os.stat(keypair_path)
        file_perms = oct(file_stat.st_mode)[-3:]
        
        print()
        print("🛡️ Security Status:")
        print(f"   File permissions: {file_perms} {'✅ SECURE' if file_perms == '600' else '⚠️ INSECURE'}")
        print(f"   File size: {len(saved_keypair_data)} bytes")
        print(f"   Keypair format: JSON array ✅")
        
        print()
        print("🎉 SUCCESS: Keypair created and saved securely!")
        print("=" * 45)
        print(f"📍 Wallet Address: {wallet_address}")
        print(f"📁 Keypair File: {keypair_path}")
        print(f"🔒 File Permissions: 600 (secure)")
        print(f"✅ Ready for Enhanced Trading System")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: Failed to create keypair: {e}")
        return False

def check_wallet_balance():
    """Check the wallet balance."""
    import subprocess
    
    wallet_address = "J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz"
    
    try:
        print("🔄 Checking wallet balance...")
        
        # Use curl to check balance via Helius
        cmd = [
            'curl', '-s',
            'https://mainnet.helius-rpc.com/?api-key=dda9f776-9a40-447d-9ca4-22a27c21169e',
            '-X', 'POST',
            '-H', 'Content-Type: application/json',
            '-d', f'{{"jsonrpc":"2.0","id":1,"method":"getBalance","params":["{wallet_address}"]}}'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            import json
            response = json.loads(result.stdout)
            if 'result' in response and 'value' in response['result']:
                balance_lamports = response['result']['value']
                balance_sol = balance_lamports / 1_000_000_000
                print(f"💰 Wallet Balance: {balance_sol:.9f} SOL ({balance_lamports} lamports)")
                
                if balance_sol > 0:
                    print("✅ Wallet has funds - ready for live trading!")
                else:
                    print("⚠️ Wallet has zero balance - needs funding for live trading")
                
                return balance_sol
            else:
                print("❌ Error getting balance from response")
        else:
            print("❌ Error checking balance")
            
    except Exception as e:
        print(f"❌ Error checking wallet balance: {e}")
    
    return 0

def main():
    """Main function."""
    print("🔐 Direct Keypair Creation Tool")
    print("=" * 35)
    print()
    
    # Create keypair
    success = create_keypair_direct()
    
    if success:
        print()
        # Check wallet balance
        balance = check_wallet_balance()
        
        print()
        print("🚀 Next Steps:")
        print("1. Test the keypair: python3 scripts/test_transaction_fixes.py")
        
        if balance > 0:
            print("2. Start live trading: python3 scripts/enhanced_live_trading.py --duration 0.5")
        else:
            print("2. Fund your wallet first, then start trading")
            
        print("3. Launch dashboard: streamlit run scripts/update_dashboard_for_production.py")
    else:
        print("❌ Keypair creation failed.")

if __name__ == "__main__":
    main()
