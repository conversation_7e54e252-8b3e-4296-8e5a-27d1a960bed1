#!/usr/bin/env python3
"""
Live Production Deployment Test
Tests actual trade execution in live production mode with real assets.
"""

import asyncio
import logging
import json
import os
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any
import subprocess

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/live_production_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LiveProductionTester:
    """Test live production deployment with actual trade execution."""

    def __init__(self):
        """Initialize the live production tester."""
        self.test_results = {
            'start_time': datetime.now().isoformat(),
            'pre_deployment_checks': {},
            'deployment_status': {},
            'trade_execution_results': {},
            'post_deployment_analysis': {},
            'recommendations': []
        }
        self.initial_balance = None
        self.final_balance = None

    async def run_pre_deployment_checks(self) -> bool:
        """Run comprehensive pre-deployment validation."""
        logger.info("🔍 Running Pre-Deployment Checks")

        checks = {}
        all_passed = True

        # Check environment configuration
        try:
            required_env_vars = [
                'HELIUS_API_KEY', 'BIRDEYE_API_KEY', 'WALLET_ADDRESS',
                'KEYPAIR_PATH', 'TELEGRAM_BOT_TOKEN', 'TELEGRAM_CHAT_ID'
            ]

            missing_vars = []
            for var in required_env_vars:
                if not os.getenv(var):
                    missing_vars.append(var)

            if missing_vars:
                checks['environment'] = {
                    'status': 'FAIL',
                    'details': f'Missing environment variables: {missing_vars}'
                }
                all_passed = False
            else:
                checks['environment'] = {
                    'status': 'PASS',
                    'details': 'All required environment variables present'
                }

        except Exception as e:
            checks['environment'] = {'status': 'FAIL', 'details': f'Environment check error: {str(e)}'}
            all_passed = False

        # Check wallet configuration and balance
        try:
            wallet_address = os.getenv('WALLET_ADDRESS')
            keypair_path = os.getenv('KEYPAIR_PATH')

            if os.path.exists(keypair_path):
                # Get wallet balance
                from phase_4_deployment.apis.helius_client import HeliusClient
                helius_client = HeliusClient(api_key=os.getenv('HELIUS_API_KEY'))

                balance_response = await helius_client.get_balance(wallet_address)
                if balance_response and 'balance' in balance_response:
                    balance = balance_response['balance']
                    self.initial_balance = balance

                    if balance > 0.1:  # Minimum 0.1 SOL for trading
                        checks['wallet_balance'] = {
                            'status': 'PASS',
                            'details': f'Wallet balance: {balance:.4f} SOL'
                        }
                    else:
                        checks['wallet_balance'] = {
                            'status': 'FAIL',
                            'details': f'Insufficient balance: {balance:.4f} SOL (minimum 0.1 SOL required)'
                        }
                        all_passed = False
                else:
                    checks['wallet_balance'] = {
                        'status': 'FAIL',
                        'details': 'Could not retrieve wallet balance'
                    }
                    all_passed = False
            else:
                checks['wallet_keypair'] = {
                    'status': 'FAIL',
                    'details': f'Keypair file not found: {keypair_path}'
                }
                all_passed = False

        except Exception as e:
            checks['wallet_setup'] = {'status': 'FAIL', 'details': f'Wallet setup error: {str(e)}'}
            all_passed = False

        # Check API connectivity
        try:
            from phase_4_deployment.apis.helius_client import HeliusClient
            from phase_4_deployment.apis.birdeye_client import BirdeyeClient

            # Test Helius connectivity
            helius_client = HeliusClient(api_key=os.getenv('HELIUS_API_KEY'))
            # Test with a simple RPC call instead of check_health
            test_blockhash = await helius_client.get_recent_blockhash()

            if test_blockhash:
                checks['helius_api'] = {'status': 'PASS', 'details': 'Helius API connectivity confirmed'}
            else:
                checks['helius_api'] = {'status': 'FAIL', 'details': 'Helius API connectivity failed'}
                all_passed = False

            # Test Birdeye connectivity
            birdeye_client = BirdeyeClient(api_key=os.getenv('BIRDEYE_API_KEY'))
            # Test with a simple API call instead of check_health
            try:
                test_response = await birdeye_client.get_token_list()
                if test_response:
                    checks['birdeye_api'] = {'status': 'PASS', 'details': 'Birdeye API connectivity confirmed'}
                else:
                    checks['birdeye_api'] = {'status': 'FAIL', 'details': 'Birdeye API connectivity failed'}
                    all_passed = False
            except Exception as birdeye_error:
                checks['birdeye_api'] = {'status': 'FAIL', 'details': f'Birdeye API error: {str(birdeye_error)}'}
                all_passed = False

        except Exception as e:
            checks['api_connectivity'] = {'status': 'FAIL', 'details': f'API connectivity error: {str(e)}'}
            all_passed = False

        # Check system resources
        try:
            import psutil
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()

            if cpu_percent < 80 and memory.percent < 80:
                checks['system_resources'] = {
                    'status': 'PASS',
                    'details': f'CPU: {cpu_percent}%, Memory: {memory.percent}%'
                }
            else:
                checks['system_resources'] = {
                    'status': 'WARN',
                    'details': f'High resource usage - CPU: {cpu_percent}%, Memory: {memory.percent}%'
                }

        except Exception as e:
            checks['system_resources'] = {'status': 'FAIL', 'details': f'System resource check error: {str(e)}'}
            all_passed = False

        self.test_results['pre_deployment_checks'] = checks

        # Print results
        logger.info("Pre-deployment check results:")
        for check_name, result in checks.items():
            status_icon = "✅" if result['status'] == 'PASS' else "⚠️" if result['status'] == 'WARN' else "❌"
            logger.info(f"  {status_icon} {check_name}: {result['status']} - {result['details']}")

        return all_passed

    async def deploy_live_production(self, duration_minutes: float = 30) -> bool:
        """Deploy live production trading system."""
        logger.info(f"🚀 Deploying Live Production Trading for {duration_minutes} minutes")

        try:
            # Set environment variables for live production
            os.environ['TRADING_ENABLED'] = 'true'
            os.environ['PAPER_TRADING'] = 'false'
            os.environ['DRY_RUN'] = 'false'

            # Start the live production system
            from scripts.start_live_production import ProductionTradingSystem

            # Use live production configuration
            config_path = "config/live_production.yaml"
            trading_system = ProductionTradingSystem(config_path)

            # Run for specified duration
            logger.info(f"Starting live trading session for {duration_minutes} minutes...")

            # Create a task for the trading system
            trading_task = asyncio.create_task(trading_system.run_trading_session())

            # Wait for the specified duration
            await asyncio.sleep(duration_minutes * 60)

            # Stop the trading system gracefully
            trading_task.cancel()

            try:
                await trading_task
            except asyncio.CancelledError:
                logger.info("Trading session completed successfully")

            self.test_results['deployment_status'] = {
                'status': 'SUCCESS',
                'duration_minutes': duration_minutes,
                'details': 'Live production deployment completed successfully'
            }

            return True

        except Exception as e:
            logger.error(f"Deployment error: {str(e)}")
            self.test_results['deployment_status'] = {
                'status': 'FAILED',
                'error': str(e),
                'details': 'Live production deployment failed'
            }
            return False

    async def analyze_trade_execution(self) -> Dict:
        """Analyze trade execution results."""
        logger.info("📊 Analyzing Trade Execution Results")

        analysis = {}

        try:
            # Check for trade files
            trades_dir = 'output/live_production/trades'
            if os.path.exists(trades_dir):
                trade_files = [f for f in os.listdir(trades_dir) if f.endswith('.json')]

                if trade_files:
                    total_trades = len(trade_files)
                    successful_trades = 0
                    total_pnl = 0.0
                    total_fees = 0.0

                    for trade_file in trade_files:
                        try:
                            with open(os.path.join(trades_dir, trade_file), 'r') as f:
                                trade_data = json.load(f)

                            if trade_data.get('status') == 'executed':
                                successful_trades += 1
                                total_pnl += trade_data.get('pnl', 0.0)
                                total_fees += trade_data.get('fees', 0.0)

                        except Exception as e:
                            logger.warning(f"Error reading trade file {trade_file}: {str(e)}")

                    analysis['trade_summary'] = {
                        'total_trades': total_trades,
                        'successful_trades': successful_trades,
                        'success_rate': (successful_trades / total_trades * 100) if total_trades > 0 else 0,
                        'total_pnl': total_pnl,
                        'total_fees': total_fees,
                        'net_pnl': total_pnl - total_fees
                    }
                else:
                    analysis['trade_summary'] = {
                        'total_trades': 0,
                        'message': 'No trades executed during test period'
                    }
            else:
                analysis['trade_summary'] = {
                    'total_trades': 0,
                    'message': 'Trades directory not found'
                }

            # Get final wallet balance
            try:
                wallet_address = os.getenv('WALLET_ADDRESS')
                from phase_4_deployment.apis.helius_client import HeliusClient
                helius_client = HeliusClient(api_key=os.getenv('HELIUS_API_KEY'))

                balance_response = await helius_client.get_balance(wallet_address)
                if balance_response and 'balance' in balance_response:
                    self.final_balance = balance_response['balance']

                    if self.initial_balance is not None:
                        balance_change = self.final_balance - self.initial_balance
                        analysis['balance_analysis'] = {
                            'initial_balance': self.initial_balance,
                            'final_balance': self.final_balance,
                            'balance_change': balance_change,
                            'balance_change_pct': (balance_change / self.initial_balance * 100) if self.initial_balance > 0 else 0
                        }
                    else:
                        analysis['balance_analysis'] = {
                            'final_balance': self.final_balance,
                            'message': 'Initial balance not recorded'
                        }

            except Exception as e:
                analysis['balance_analysis'] = {'error': f'Balance analysis error: {str(e)}'}

        except Exception as e:
            analysis['error'] = f'Trade analysis error: {str(e)}'

        self.test_results['trade_execution_results'] = analysis
        return analysis

    async def generate_deployment_report(self) -> str:
        """Generate comprehensive deployment test report."""
        self.test_results['end_time'] = datetime.now().isoformat()

        # Calculate test duration
        start_time = datetime.fromisoformat(self.test_results['start_time'])
        end_time = datetime.fromisoformat(self.test_results['end_time'])
        duration = end_time - start_time

        self.test_results['test_duration_minutes'] = duration.total_seconds() / 60

        # Generate recommendations
        recommendations = []

        # Check pre-deployment results
        pre_checks = self.test_results.get('pre_deployment_checks', {})
        failed_checks = [name for name, result in pre_checks.items() if result['status'] == 'FAIL']
        if failed_checks:
            recommendations.append(f"Fix failed pre-deployment checks: {', '.join(failed_checks)}")

        # Check trade execution
        trade_results = self.test_results.get('trade_execution_results', {})
        trade_summary = trade_results.get('trade_summary', {})

        if trade_summary.get('total_trades', 0) == 0:
            recommendations.append("No trades executed - check strategy parameters and market conditions")
        elif trade_summary.get('success_rate', 0) < 50:
            recommendations.append("Low trade success rate - review strategy logic and risk parameters")

        # Check balance changes
        balance_analysis = trade_results.get('balance_analysis', {})
        if 'balance_change' in balance_analysis and balance_analysis['balance_change'] < 0:
            recommendations.append("Negative balance change detected - review risk management settings")

        self.test_results['recommendations'] = recommendations

        # Save report
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = f"output/live_production_test_report_{timestamp}.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)

        with open(report_path, 'w') as f:
            json.dump(self.test_results, f, indent=2)

        logger.info(f"📄 Live production test report saved: {report_path}")
        return report_path


async def main():
    """Main function to run live production deployment test."""
    import argparse

    parser = argparse.ArgumentParser(description="Test Live Production Deployment")
    parser.add_argument("--duration", type=float, default=30.0,
                       help="Test duration in minutes (default: 30)")
    parser.add_argument("--validate-only", action="store_true",
                       help="Only run pre-deployment validation")

    args = parser.parse_args()

    tester = LiveProductionTester()

    print("🚀 LIVE PRODUCTION DEPLOYMENT TEST")
    print("="*60)
    print("⚠️  WARNING: This will execute REAL TRADES with REAL ASSETS")
    print("="*60)

    # Run pre-deployment checks
    pre_checks_passed = await tester.run_pre_deployment_checks()

    if not pre_checks_passed:
        print("❌ Pre-deployment checks failed. Fix issues before proceeding.")
        return 1

    if args.validate_only:
        print("✅ Pre-deployment validation completed successfully")
        return 0

    # Confirm live trading
    print(f"\n🎯 Ready to start live trading for {args.duration} minutes")
    print("⚠️  This will use REAL MONEY and execute REAL TRADES")

    # Deploy live production
    deployment_success = await tester.deploy_live_production(args.duration)

    if deployment_success:
        # Analyze results
        await tester.analyze_trade_execution()

        # Generate report
        report_path = await tester.generate_deployment_report()

        print("\n📊 LIVE PRODUCTION TEST RESULTS")
        print("="*60)

        # Print summary
        trade_results = tester.test_results.get('trade_execution_results', {})
        trade_summary = trade_results.get('trade_summary', {})

        if 'total_trades' in trade_summary:
            print(f"Total Trades: {trade_summary['total_trades']}")
            print(f"Successful Trades: {trade_summary.get('successful_trades', 0)}")
            print(f"Success Rate: {trade_summary.get('success_rate', 0):.1f}%")
            print(f"Net PnL: {trade_summary.get('net_pnl', 0):.4f} SOL")

        balance_analysis = trade_results.get('balance_analysis', {})
        if 'balance_change' in balance_analysis:
            print(f"Balance Change: {balance_analysis['balance_change']:.4f} SOL")
            print(f"Balance Change %: {balance_analysis['balance_change_pct']:.2f}%")

        print(f"\n📄 Full report: {report_path}")
        print("="*60)

        return 0 if deployment_success else 1
    else:
        print("❌ Live production deployment failed")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
