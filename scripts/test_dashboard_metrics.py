#!/usr/bin/env python3
"""
Dashboard Metrics Testing Script
Tests dashboard data accuracy and real-time metric display.
"""

import asyncio
import logging
import json
import os
import sys
import time
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import requests
import pandas as pd

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DashboardMetricsTester:
    """Test dashboard metrics accuracy and functionality."""
    
    def __init__(self):
        """Initialize the dashboard metrics tester."""
        self.test_results = {}
        self.dashboard_processes = []
        
    async def test_dashboard_data_accuracy(self) -> Dict[str, Any]:
        """Test dashboard data accuracy."""
        logger.info("🔍 Testing Dashboard Data Accuracy")
        
        results = {}
        
        # Test 1: Data Source Consistency
        results['data_source_consistency'] = await self.test_data_source_consistency()
        
        # Test 2: Metric Calculations
        results['metric_calculations'] = await self.test_metric_calculations()
        
        # Test 3: Real-time Updates
        results['realtime_updates'] = await self.test_realtime_updates()
        
        # Test 4: Dashboard Accessibility
        results['dashboard_accessibility'] = await self.test_dashboard_accessibility()
        
        return results
    
    async def test_data_source_consistency(self) -> Dict[str, Any]:
        """Test consistency between data sources."""
        logger.info("Testing data source consistency...")
        
        test_result = {'status': 'PASS', 'details': [], 'issues': []}
        
        try:
            # Check enhanced live trading data
            enhanced_path = 'output/enhanced_live_trading/latest_metrics.json'
            if os.path.exists(enhanced_path):
                with open(enhanced_path, 'r') as f:
                    enhanced_data = json.load(f)
                test_result['details'].append(f"Enhanced live trading data found: {len(enhanced_data)} metrics")
            else:
                test_result['issues'].append("Enhanced live trading data not found")
            
            # Check production data
            production_path = 'output/live_production/dashboard/latest_metrics.json'
            if os.path.exists(production_path):
                with open(production_path, 'r') as f:
                    production_data = json.load(f)
                test_result['details'].append(f"Production data found: {len(production_data)} metrics")
            else:
                test_result['issues'].append("Production data not found")
            
            # Check wallet data
            wallet_path = 'output/wallet/wallet_balance.json'
            if os.path.exists(wallet_path):
                with open(wallet_path, 'r') as f:
                    wallet_data = json.load(f)
                test_result['details'].append(f"Wallet data found: {wallet_data.get('balance', 'N/A')} SOL")
            else:
                test_result['issues'].append("Wallet data not found")
            
            # Check for data consistency
            if len(test_result['issues']) > len(test_result['details']):
                test_result['status'] = 'FAIL'
                
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['issues'].append(f"Data source consistency error: {str(e)}")
        
        return test_result
    
    async def test_metric_calculations(self) -> Dict[str, Any]:
        """Test metric calculation accuracy."""
        logger.info("Testing metric calculations...")
        
        test_result = {'status': 'PASS', 'details': [], 'issues': []}
        
        try:
            # Load trade data for calculation verification
            trades_path = 'output/enhanced_live_trading/trades'
            if os.path.exists(trades_path):
                trade_files = [f for f in os.listdir(trades_path) if f.endswith('.json')]
                
                if trade_files:
                    # Calculate PnL manually
                    total_pnl = 0.0
                    trade_count = 0
                    
                    for trade_file in trade_files[-10:]:  # Check last 10 trades
                        try:
                            with open(os.path.join(trades_path, trade_file), 'r') as f:
                                trade_data = json.load(f)
                            
                            # Extract PnL if available
                            if 'pnl' in trade_data:
                                total_pnl += trade_data['pnl']
                                trade_count += 1
                                
                        except Exception as e:
                            test_result['issues'].append(f"Error reading trade file {trade_file}: {str(e)}")
                    
                    test_result['details'].append(f"Calculated PnL from {trade_count} trades: {total_pnl:.4f}")
                    
                    # Compare with dashboard metrics
                    metrics_path = 'output/enhanced_live_trading/latest_metrics.json'
                    if os.path.exists(metrics_path):
                        with open(metrics_path, 'r') as f:
                            metrics = json.load(f)
                        
                        dashboard_pnl = metrics.get('total_pnl', 0.0)
                        pnl_difference = abs(total_pnl - dashboard_pnl)
                        
                        if pnl_difference < 0.001:  # Allow small floating point differences
                            test_result['details'].append(f"PnL calculation matches dashboard: {dashboard_pnl:.4f}")
                        else:
                            test_result['issues'].append(f"PnL mismatch: calculated {total_pnl:.4f}, dashboard {dashboard_pnl:.4f}")
                            test_result['status'] = 'FAIL'
                    
                else:
                    test_result['issues'].append("No trade files found for calculation verification")
            else:
                test_result['issues'].append("Trades directory not found")
                
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['issues'].append(f"Metric calculation error: {str(e)}")
        
        return test_result
    
    async def test_realtime_updates(self) -> Dict[str, Any]:
        """Test real-time dashboard updates."""
        logger.info("Testing real-time updates...")
        
        test_result = {'status': 'PASS', 'details': [], 'issues': []}
        
        try:
            # Check if metrics files have recent timestamps
            metrics_files = [
                'output/enhanced_live_trading/latest_metrics.json',
                'output/live_production/dashboard/latest_metrics.json',
                'output/wallet/wallet_balance.json'
            ]
            
            current_time = datetime.now()
            recent_updates = 0
            
            for metrics_file in metrics_files:
                if os.path.exists(metrics_file):
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(metrics_file))
                    time_diff = current_time - file_mtime
                    
                    if time_diff.total_seconds() < 3600:  # Updated within last hour
                        recent_updates += 1
                        test_result['details'].append(f"{metrics_file}: Updated {time_diff.total_seconds():.0f}s ago")
                    else:
                        test_result['issues'].append(f"{metrics_file}: Last updated {time_diff.total_seconds():.0f}s ago (stale)")
                else:
                    test_result['issues'].append(f"{metrics_file}: File not found")
            
            if recent_updates == 0:
                test_result['status'] = 'FAIL'
                test_result['issues'].append("No recent metric updates found")
            else:
                test_result['details'].append(f"{recent_updates}/{len(metrics_files)} metrics files recently updated")
                
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['issues'].append(f"Real-time update test error: {str(e)}")
        
        return test_result
    
    async def test_dashboard_accessibility(self) -> Dict[str, Any]:
        """Test dashboard accessibility."""
        logger.info("Testing dashboard accessibility...")
        
        test_result = {'status': 'PASS', 'details': [], 'issues': []}
        
        try:
            # Check if dashboard scripts exist
            dashboard_scripts = [
                'enhanced_trading_dashboard.py',
                'simple_monitoring_dashboard.py',
                'scripts/update_dashboard_for_production.py'
            ]
            
            accessible_dashboards = 0
            for script in dashboard_scripts:
                if os.path.exists(script):
                    accessible_dashboards += 1
                    test_result['details'].append(f"Dashboard script found: {script}")
                else:
                    test_result['issues'].append(f"Dashboard script not found: {script}")
            
            if accessible_dashboards == 0:
                test_result['status'] = 'FAIL'
                test_result['issues'].append("No dashboard scripts found")
            else:
                test_result['details'].append(f"{accessible_dashboards}/{len(dashboard_scripts)} dashboard scripts available")
            
            # Test if Streamlit is available
            try:
                result = subprocess.run(['streamlit', '--version'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    test_result['details'].append(f"Streamlit available: {result.stdout.strip()}")
                else:
                    test_result['issues'].append("Streamlit not available or not working")
                    test_result['status'] = 'FAIL'
            except Exception as e:
                test_result['issues'].append(f"Streamlit test error: {str(e)}")
                test_result['status'] = 'FAIL'
                
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['issues'].append(f"Dashboard accessibility error: {str(e)}")
        
        return test_result
    
    async def generate_dashboard_test_report(self, results: Dict[str, Any]) -> str:
        """Generate dashboard test report."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = f"output/dashboard_test_report_{timestamp}.json"
        
        # Create comprehensive report
        report = {
            'timestamp': timestamp,
            'test_results': results,
            'summary': {
                'total_tests': len(results),
                'passed_tests': len([r for r in results.values() if r['status'] == 'PASS']),
                'failed_tests': len([r for r in results.values() if r['status'] == 'FAIL'])
            }
        }
        
        # Save report
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"📊 Dashboard test report saved: {report_path}")
        return report_path


async def main():
    """Main function to run dashboard metrics tests."""
    tester = DashboardMetricsTester()
    
    print("🚀 Starting Dashboard Metrics Testing")
    print("="*60)
    
    # Run dashboard tests
    results = await tester.test_dashboard_data_accuracy()
    
    # Generate report
    report_path = await tester.generate_dashboard_test_report(results)
    
    # Print summary
    print("\n📊 DASHBOARD METRICS TEST RESULTS")
    print("="*60)
    
    for test_name, result in results.items():
        status_icon = "✅" if result['status'] == 'PASS' else "❌"
        print(f"{status_icon} {test_name.replace('_', ' ').title()}: {result['status']}")
        
        if result.get('details'):
            for detail in result['details']:
                print(f"   ✓ {detail}")
        
        if result.get('issues'):
            for issue in result['issues']:
                print(f"   ⚠️ {issue}")
    
    print("="*60)
    print(f"📄 Full report saved to: {report_path}")
    
    return results


if __name__ == "__main__":
    asyncio.run(main())
