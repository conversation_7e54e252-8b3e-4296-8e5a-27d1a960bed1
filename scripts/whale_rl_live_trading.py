#!/usr/bin/env python3
"""
Whale + RL Enhanced Live Trading System
The ultimate trading system combining whale intelligence with reinforcement learning.
"""

import asyncio
import argparse
import logging
import sys
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import base components
from scripts.enhanced_live_trading import EnhancedLiveTradingSystem

# Import whale components
from core.whale.whale_detector import WhaleDetector
from core.whale.whale_signal_generator import WhaleSignalGenerator

# Import enhanced RL components
from core.learning.enhanced_adaptive_manager import EnhancedAdaptiveManager

logger = logging.getLogger(__name__)

class WhaleRLLiveTradingSystem(EnhancedLiveTradingSystem):
    """Ultimate trading system with whale intelligence and reinforcement learning."""
    
    def __init__(self, config_path: str = "config/live_production.yaml"):
        """Initialize whale+RL enhanced live trading system."""
        super().__init__(config_path)
        
        # Load whale configuration
        self.whale_config = self._load_whale_config()
        
        # Whale components
        self.whale_detector = None
        self.whale_signal_generator = None
        
        # Enhanced RL components
        self.enhanced_adaptive_manager = None
        
        # Whale intelligence settings
        self.whale_enabled = self.whale_config.get('whale_detection', {}).get('enabled', True)
        self.whale_learning_enabled = self.whale_config.get('whale_detection', {}).get('rl_learning_enabled', True)
        
        # Whale signal tracking
        self.current_whale_signals = []
        self.whale_signal_history = []
        
        # Performance tracking
        self.whale_enhanced_trades = 0
        self.whale_confirmed_trades = 0
        self.whale_override_trades = 0
        
        logger.info(f"🐋🧠 Whale+RL Live Trading System initialized")
        logger.info(f"Whale detection: {'ON' if self.whale_enabled else 'OFF'}")
        logger.info(f"Whale learning: {'ON' if self.whale_learning_enabled else 'OFF'}")
    
    def _load_whale_config(self) -> Dict[str, Any]:
        """Load whale-specific configuration."""
        try:
            import yaml
            whale_config_path = "config/whale_config.yaml"
            
            if os.path.exists(whale_config_path):
                with open(whale_config_path, 'r') as f:
                    whale_config = yaml.safe_load(f)
                logger.info("✅ Whale configuration loaded")
                return whale_config
            else:
                logger.warning("⚠️ Whale config not found, using defaults")
                return {}
                
        except Exception as e:
            logger.error(f"Error loading whale config: {e}")
            return {}
    
    async def initialize_components(self) -> bool:
        """Initialize all components including whale intelligence."""
        
        # Initialize base components first
        if not await super().initialize_components():
            return False
        
        # Initialize whale components
        if self.whale_enabled:
            try:
                # Initialize whale detector
                combined_config = {**self.config, **self.whale_config}
                self.whale_detector = WhaleDetector(combined_config)
                
                if not await self.whale_detector.initialize():
                    logger.error("Failed to initialize whale detector")
                    return False
                
                logger.info("✅ Whale detector initialized")
                
                # Initialize whale signal generator
                self.whale_signal_generator = WhaleSignalGenerator(combined_config)
                logger.info("✅ Whale signal generator initialized")
                
                # Start whale monitoring in background
                asyncio.create_task(self.whale_detector.start_monitoring())
                logger.info("🐋 Whale monitoring started")
                
            except Exception as e:
                logger.error(f"Error initializing whale components: {e}")
                return False
        
        # Initialize enhanced adaptive manager (replaces basic one)
        if self.learning_enabled:
            try:
                combined_config = {**self.config, **self.whale_config}
                self.enhanced_adaptive_manager = EnhancedAdaptiveManager(combined_config)
                
                # Replace the basic adaptive manager
                self.adaptive_manager = self.enhanced_adaptive_manager
                
                logger.info("✅ Enhanced Adaptive Manager (Whale+RL) initialized")
                
            except Exception as e:
                logger.error(f"Error initializing enhanced adaptive manager: {e}")
                return False
        
        # Send initialization notification
        if self.telegram_notifier and self.telegram_notifier.enabled:
            await self.telegram_notifier.send_message(
                "🐋🧠 *WHALE+RL TRADING SYSTEM ACTIVATED* 🧠🐋\n\n"
                f"✅ Whale detection: {'ENABLED' if self.whale_enabled else 'DISABLED'}\n"
                f"✅ Whale learning: {'ENABLED' if self.whale_learning_enabled else 'DISABLED'}\n"
                f"✅ RL adaptation: {'ENABLED' if self.learning_enabled else 'DISABLED'}\n\n"
                "🚀 Ultimate trading intelligence online!\n"
                "📊 Whale signals + AI learning = Maximum profit potential"
            )
        
        return True
    
    async def execute_trading_cycle(self) -> Dict[str, Any]:
        """Execute enhanced trading cycle with whale intelligence."""
        
        cycle_data = {
            'cycle_id': f"whale_rl_cycle_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'timestamp': datetime.now().isoformat(),
            'status': 'started',
            'whale_enabled': self.whale_enabled,
            'whale_learning_enabled': self.whale_learning_enabled
        }
        
        try:
            # 1. Get whale intelligence
            whale_signals = []
            if self.whale_enabled and self.whale_detector:
                recent_whales = self.whale_detector.get_recent_whales(hours=1)
                if recent_whales and self.whale_signal_generator:
                    whale_signals = self.whale_signal_generator.generate_signals(recent_whales)
                    
                    # Record whale signals for learning
                    for whale_signal in whale_signals:
                        if self.enhanced_adaptive_manager:
                            self.enhanced_adaptive_manager.record_whale_signal(whale_signal)
                    
                    self.current_whale_signals = whale_signals
                    self.whale_signal_history.extend(whale_signals)
                    
                    cycle_data['whale_signals'] = [
                        {
                            'type': ws.signal_type,
                            'action': ws.action,
                            'confidence': ws.confidence,
                            'strength': ws.strength,
                            'timeframe': ws.timeframe
                        } for ws in whale_signals
                    ]
                    
                    logger.info(f"🐋 Generated {len(whale_signals)} whale signals")
            
            # 2. Generate base trading signal
            base_signal = await self.generate_trading_signal()
            cycle_data['base_signal'] = base_signal
            
            if not base_signal or base_signal.get('action') == 'HOLD':
                cycle_data['status'] = 'no_base_signal'
                return cycle_data
            
            # 3. Apply whale+RL enhancements
            enhanced_signal = base_signal
            if self.enhanced_adaptive_manager:
                enhanced_signal = self.enhanced_adaptive_manager.get_whale_enhanced_signal(
                    base_signal, whale_signals
                )
                cycle_data['enhanced_signal'] = enhanced_signal
                
                # Track enhancement types
                if enhanced_signal.get('whale_confirmation'):
                    self.whale_confirmed_trades += 1
                    logger.info("🐋✅ Whale confirmed signal")
                
                if enhanced_signal.get('whale_override'):
                    self.whale_override_trades += 1
                    logger.info(f"🐋🔄 Whale override: {enhanced_signal.get('override_reason')}")
                
                if enhanced_signal.get('whale_enhanced'):
                    self.whale_enhanced_trades += 1
            
            # 4. Check if signal was adapted to HOLD
            if enhanced_signal.get('action') == 'HOLD':
                cycle_data['status'] = 'enhanced_to_hold'
                cycle_data['hold_reason'] = enhanced_signal.get('adaptation_reason', 'Whale/RL adaptation')
                return cycle_data
            
            # 5. Calculate position size
            position_data = await self.position_sizer.calculate_position_size(enhanced_signal)
            cycle_data['position_data'] = position_data
            
            # 6. Check for rejection
            if position_data.get('rejected', False):
                rejection_reason = position_data.get('rejection_reason', 'Unknown reason')
                logger.warning(f"Position rejected: {rejection_reason}")
                
                # Record rejection for learning
                if self.enhanced_adaptive_manager:
                    self.enhanced_adaptive_manager.record_trade_result_with_whale(
                        enhanced_signal, 
                        {'success': False, 'rejection_reason': rejection_reason}, 
                        0.0,
                        whale_signals
                    )
                
                # Send Telegram notification
                if self.telegram_notifier and self.telegram_notifier.enabled:
                    await self.telegram_notifier.notify_trade_rejected(enhanced_signal, rejection_reason)
                
                cycle_data['status'] = 'rejected'
                cycle_data['rejection_reason'] = rejection_reason
                self.metrics['trades_rejected'] += 1
                return cycle_data
            
            # 7. Build and execute transaction
            transaction = await self.tx_builder.build_transaction(enhanced_signal)
            
            if not transaction:
                logger.error("Failed to build transaction")
                cycle_data['status'] = 'build_failed'
                return cycle_data
            
            # 8. Execute transaction
            result = await self.tx_executor.send_transaction(transaction)
            cycle_data['transaction_result'] = result
            
            # 9. Calculate PnL and record results
            trade_pnl = 0.0
            if result.get('success', False):
                current_balance = await self.wallet_manager.get_balance()
                trade_pnl = current_balance - self.last_balance if hasattr(self, 'last_balance') else 0.0
                self.last_balance = current_balance
                
                logger.info(f"✅ Whale+RL trade executed: {result.get('signature')}")
                logger.info(f"💰 Trade PnL: {trade_pnl:+.6f} SOL")
                
                self.trades_executed += 1
                self.metrics['trades_executed'] += 1
                
                # Record for enhanced learning
                if self.enhanced_adaptive_manager:
                    self.enhanced_adaptive_manager.record_trade_result_with_whale(
                        enhanced_signal, result, trade_pnl, whale_signals
                    )
                
                # Create enhanced trade data
                trade_data = {
                    'signal': enhanced_signal,
                    'whale_signals': [
                        {
                            'type': ws.signal_type,
                            'action': ws.action,
                            'confidence': ws.confidence,
                            'timeframe': ws.timeframe
                        } for ws in whale_signals
                    ],
                    'position_data': position_data,
                    'transaction_result': result,
                    'timestamp': datetime.now().isoformat(),
                    'trade_pnl': trade_pnl,
                    'whale_enhanced': enhanced_signal.get('whale_enhanced', False),
                    'whale_confirmation': enhanced_signal.get('whale_confirmation', False),
                    'whale_override': enhanced_signal.get('whale_override', False)
                }
                
                # Save enhanced trade data
                await self._save_whale_trade_data(trade_data)
                
                # Send enhanced Telegram notification
                if self.telegram_notifier and self.telegram_notifier.enabled:
                    await self._send_whale_trade_notification(trade_data)
                
                cycle_data['status'] = 'completed'
                cycle_data['trade_executed'] = True
                cycle_data['signature'] = result.get('signature')
                cycle_data['trade_pnl'] = trade_pnl
                cycle_data['whale_enhanced'] = enhanced_signal.get('whale_enhanced', False)
                
            else:
                logger.error(f"❌ Transaction failed: {result.get('error')}")
                
                # Record failed trade
                if self.enhanced_adaptive_manager:
                    self.enhanced_adaptive_manager.record_trade_result_with_whale(
                        enhanced_signal, result, 0.0, whale_signals
                    )
                
                cycle_data['status'] = 'execution_failed'
                cycle_data['error'] = result.get('error')
            
            return cycle_data
            
        except Exception as e:
            logger.error(f"Error in whale+RL trading cycle: {e}")
            cycle_data['status'] = 'error'
            cycle_data['error'] = str(e)
            return cycle_data
    
    async def _save_whale_trade_data(self, trade_data: Dict[str, Any]) -> None:
        """Save whale-enhanced trade data."""
        try:
            os.makedirs("output/whale_rl_trading/trades", exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"whale_rl_trade_{timestamp}.json"
            filepath = os.path.join("output/whale_rl_trading/trades", filename)
            
            with open(filepath, 'w') as f:
                json.dump(trade_data, f, indent=2, default=str)
                
            logger.debug(f"Whale+RL trade data saved: {filename}")
            
        except Exception as e:
            logger.error(f"Error saving whale trade data: {e}")
    
    async def _send_whale_trade_notification(self, trade_data: Dict[str, Any]) -> None:
        """Send enhanced Telegram notification for whale+RL trades."""
        try:
            signal = trade_data['signal']
            whale_signals = trade_data.get('whale_signals', [])
            result = trade_data['transaction_result']
            trade_pnl = trade_data.get('trade_pnl', 0.0)
            
            # Build enhanced message
            whale_info = ""
            if trade_data.get('whale_enhanced'):
                whale_count = len(whale_signals)
                whale_types = list(set(ws['type'] for ws in whale_signals))
                whale_info = f"\n🐋 *Whale Enhanced*: {whale_count} signals ({', '.join(whale_types)})"
                
                if trade_data.get('whale_confirmation'):
                    whale_info += "\n✅ *Whale Confirmed*"
                
                if trade_data.get('whale_override'):
                    whale_info += "\n🔄 *Whale Override*"
            
            # Calculate session PnL if available
            session_pnl_text = ""
            if hasattr(self, 'session_start_balance') and hasattr(self, 'last_balance'):
                session_pnl = self.last_balance - self.session_start_balance
                session_pnl_usd = session_pnl * 180.0  # Approximate
                session_pnl_text = f"\n📊 *Session PnL*: {session_pnl:+.6f} SOL (${session_pnl_usd:+.2f})"
            
            message = f"""
🐋🧠 *WHALE+RL TRADE EXECUTED* 🧠🐋

*Action*: {signal.get('action')}
*Size*: {signal.get('size', 0):.4f} SOL
*Price*: ${signal.get('price', 0):.2f}
*Confidence*: {signal.get('confidence', 0):.1%}
*Strategy*: {signal.get('strategy', 'Unknown')}

💰 *Trade PnL*: {trade_pnl:+.6f} SOL{session_pnl_text}{whale_info}

*Signature*: `{result.get('signature', 'N/A')[:16]}...`
*Time*: {datetime.now().strftime('%H:%M:%S')}

🚀 Ultimate trading intelligence in action!
"""
            
            await self.telegram_notifier.send_message(message)
            
        except Exception as e:
            logger.error(f"Error sending whale trade notification: {e}")
    
    async def get_whale_rl_metrics(self) -> Dict[str, Any]:
        """Get comprehensive whale+RL metrics."""
        
        # Get base metrics
        base_metrics = await self._get_base_metrics()
        
        # Get whale metrics
        whale_metrics = {}
        if self.whale_detector:
            whale_metrics = self.whale_detector.get_metrics()
        
        # Get whale signal metrics
        whale_signal_metrics = {}
        if self.whale_signal_generator:
            whale_signal_metrics = self.whale_signal_generator.get_signal_summary()
        
        # Get enhanced learning metrics
        learning_metrics = {}
        if self.enhanced_adaptive_manager:
            learning_metrics = self.enhanced_adaptive_manager.get_enhanced_learning_metrics()
        
        # Combine all metrics
        comprehensive_metrics = {
            'timestamp': datetime.now().isoformat(),
            'system_type': 'whale_rl_enhanced',
            'whale_enabled': self.whale_enabled,
            'whale_learning_enabled': self.whale_learning_enabled,
            'base_metrics': base_metrics,
            'whale_detection': whale_metrics,
            'whale_signals': whale_signal_metrics,
            'enhanced_learning': learning_metrics,
            'whale_trade_stats': {
                'whale_enhanced_trades': self.whale_enhanced_trades,
                'whale_confirmed_trades': self.whale_confirmed_trades,
                'whale_override_trades': self.whale_override_trades,
                'total_whale_signals': len(self.whale_signal_history)
            }
        }
        
        return comprehensive_metrics
    
    async def close(self) -> None:
        """Close whale+RL trading system and cleanup."""
        
        # Stop whale monitoring
        if self.whale_detector:
            self.whale_detector.stop_monitoring()
            await self.whale_detector.close()
        
        # Save enhanced learning state
        if self.enhanced_adaptive_manager:
            self.enhanced_adaptive_manager._save_enhanced_learning_state()
        
        # Close base components
        await super().close()
        
        logger.info("🐋🧠 Whale+RL trading system closed")


async def main():
    """Main function for whale+RL enhanced live trading."""
    
    parser = argparse.ArgumentParser(description="Whale+RL Enhanced Live Trading System")
    parser.add_argument("--duration", type=float, default=None,
                       help="Trading session duration in hours")
    parser.add_argument("--interval", type=float, default=30.0,
                       help="Cycle interval in seconds")
    parser.add_argument("--config", type=str, default="config/live_production.yaml",
                       help="Configuration file path")
    parser.add_argument("--disable-whale", action="store_true",
                       help="Disable whale detection for this session")
    parser.add_argument("--disable-learning", action="store_true",
                       help="Disable adaptive learning for this session")
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create whale+RL trading system
    trading_system = WhaleRLLiveTradingSystem(args.config)
    
    # Apply command line overrides
    if args.disable_whale:
        trading_system.whale_enabled = False
        logger.info("Whale detection disabled by command line argument")
    
    if args.disable_learning:
        trading_system.learning_enabled = False
        logger.info("Adaptive learning disabled by command line argument")
    
    try:
        success = await trading_system.run_trading_session(args.duration, args.interval)
        
        if success:
            logger.info("✅ Whale+RL trading session completed successfully")
            return 0
        else:
            logger.error("❌ Whale+RL trading session failed")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Trading session interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"Fatal error in whale+RL trading session: {e}")
        return 1
    finally:
        await trading_system.close()

if __name__ == "__main__":
    exit(asyncio.run(main()))
