#!/usr/bin/env python3
"""
Enhanced Dashboard Sync - Ensures Dashboard Reads Live Trading Data
Synchronizes dashboard metrics with the active enhanced live trading system.
"""

import streamlit as st
import json
import os
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time
from pathlib import Path
import glob

# Configure page
st.set_page_config(
    page_title="Enhanced Live Trading Dashboard",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

def load_enhanced_metrics():
    """Load metrics from enhanced live trading system."""
    try:
        metrics_file = "output/enhanced_live_trading/latest_metrics.json"
        if os.path.exists(metrics_file):
            with open(metrics_file, 'r') as f:
                return json.load(f)
    except Exception as e:
        st.error(f"Error loading enhanced metrics: {e}")
    return None

def load_enhanced_trades():
    """Load all trades from enhanced live trading system."""
    trades = []
    try:
        trade_files = glob.glob("output/enhanced_live_trading/trades/trade_*.json")
        trade_files.sort(reverse=True)  # Most recent first
        
        for trade_file in trade_files:
            try:
                with open(trade_file, 'r') as f:
                    trade_data = json.load(f)
                    trades.append(trade_data)
            except Exception as e:
                st.warning(f"Error loading trade file {trade_file}: {e}")
                
    except Exception as e:
        st.error(f"Error loading enhanced trades: {e}")
    
    return trades

def load_enhanced_cycles():
    """Load recent cycles from enhanced live trading system."""
    cycles = []
    try:
        cycle_files = glob.glob("output/enhanced_live_trading/cycles/cycle_*.json")
        cycle_files.sort(reverse=True)  # Most recent first
        
        # Load last 20 cycles for performance
        for cycle_file in cycle_files[:20]:
            try:
                with open(cycle_file, 'r') as f:
                    cycle_data = json.load(f)
                    cycles.append(cycle_data)
            except Exception as e:
                st.warning(f"Error loading cycle file {cycle_file}: {e}")
                
    except Exception as e:
        st.error(f"Error loading enhanced cycles: {e}")
    
    return cycles

def display_system_status(metrics):
    """Display system status and health."""
    if not metrics:
        st.error("❌ No metrics available - Enhanced live trading system may not be running")
        return
    
    # Calculate session info
    session_start = datetime.fromisoformat(metrics['session_start'].replace('Z', '+00:00'))
    session_duration = metrics.get('session_duration_minutes', 0)
    
    # System status
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "🟢 System Status",
            "LIVE TRADING",
            f"Running {session_duration:.1f} min"
        )
    
    with col2:
        cycles_completed = metrics['metrics']['cycles_completed']
        cycles_successful = metrics['metrics']['cycles_successful']
        success_rate = (cycles_successful / cycles_completed * 100) if cycles_completed > 0 else 0
        st.metric(
            "📊 Cycle Success",
            f"{success_rate:.1f}%",
            f"{cycles_successful}/{cycles_completed}"
        )
    
    with col3:
        executor_metrics = metrics.get('executor_metrics', {})
        tx_success_rate = executor_metrics.get('success_rate', 0) * 100
        st.metric(
            "⚡ Transaction Success",
            f"{tx_success_rate:.1f}%",
            f"{executor_metrics.get('successful_transactions', 0)} executed"
        )
    
    with col4:
        avg_execution_time = executor_metrics.get('average_execution_time', 0)
        st.metric(
            "🚀 Avg Execution Time",
            f"{avg_execution_time:.3f}s",
            "Real transactions"
        )

def display_trading_metrics(trades):
    """Display trading performance metrics."""
    if not trades:
        st.warning("No trades available yet")
        return
    
    st.subheader("💰 Trading Performance")
    
    # Calculate metrics
    total_trades = len(trades)
    successful_trades = sum(1 for trade in trades if trade.get('transaction_result', {}).get('success', False))
    
    # Position sizes and values
    position_sizes_usd = [trade['position_data']['position_size_usd'] for trade in trades]
    avg_position_size = sum(position_sizes_usd) / len(position_sizes_usd) if position_sizes_usd else 0
    
    # Transaction signatures (real trades)
    real_transactions = [trade for trade in trades if trade.get('transaction_result', {}).get('signature')]
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "📈 Total Trades",
            total_trades,
            f"{successful_trades} successful"
        )
    
    with col2:
        st.metric(
            "💵 Avg Position Size",
            f"${avg_position_size:.2f}",
            f"{avg_position_size/180:.4f} SOL"
        )
    
    with col3:
        st.metric(
            "✅ Real Transactions",
            len(real_transactions),
            f"{len(real_transactions)/total_trades*100:.1f}% executed" if total_trades > 0 else "0%"
        )
    
    with col4:
        if trades:
            latest_trade = trades[0]
            wallet_balance = latest_trade['position_data'].get('total_wallet_sol', 0)
            st.metric(
                "💰 Wallet Balance",
                f"{wallet_balance:.4f} SOL",
                f"${wallet_balance * 180:.2f}"
            )

def display_recent_trades(trades):
    """Display recent trades table."""
    if not trades:
        return
    
    st.subheader("📋 Recent Trades")
    
    # Prepare trade data for display
    trade_data = []
    for trade in trades[:10]:  # Show last 10 trades
        signal = trade.get('signal', {})
        position_data = trade.get('position_data', {})
        tx_result = trade.get('transaction_result', {})
        
        trade_data.append({
            'Time': datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00')).strftime('%H:%M:%S'),
            'Action': signal.get('action', 'N/A'),
            'Size (SOL)': f"{position_data.get('position_size_sol', 0):.4f}",
            'Size (USD)': f"${position_data.get('position_size_usd', 0):.2f}",
            'Confidence': f"{signal.get('confidence', 0):.1%}",
            'Status': '✅ Executed' if tx_result.get('success') else '❌ Failed',
            'Signature': tx_result.get('signature', 'N/A')[:16] + '...' if tx_result.get('signature') else 'N/A'
        })
    
    if trade_data:
        df = pd.DataFrame(trade_data)
        st.dataframe(df, use_container_width=True)

def display_performance_charts(trades):
    """Display performance charts."""
    if len(trades) < 2:
        return
    
    st.subheader("📊 Performance Charts")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Position sizes over time
        times = [datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00')) for trade in reversed(trades)]
        sizes = [trade['position_data']['position_size_usd'] for trade in reversed(trades)]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=times,
            y=sizes,
            mode='lines+markers',
            name='Position Size (USD)',
            line=dict(color='#00ff88')
        ))
        fig.update_layout(
            title="Position Sizes Over Time",
            xaxis_title="Time",
            yaxis_title="Position Size (USD)",
            height=400
        )
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Action distribution
        actions = [trade['signal']['action'] for trade in trades]
        action_counts = pd.Series(actions).value_counts()
        
        fig = px.pie(
            values=action_counts.values,
            names=action_counts.index,
            title="Trade Action Distribution"
        )
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)

def display_transaction_details(trades):
    """Display detailed transaction information."""
    real_trades = [trade for trade in trades if trade.get('transaction_result', {}).get('signature')]
    
    if not real_trades:
        st.info("No real transactions executed yet")
        return
    
    st.subheader("🔗 Transaction Details")
    
    for i, trade in enumerate(real_trades[:5]):  # Show last 5 real transactions
        tx_result = trade['transaction_result']
        signal = trade['signal']
        
        with st.expander(f"Transaction {i+1}: {signal['action']} {signal['size']:.4f} SOL"):
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**Transaction Info:**")
                st.write(f"Signature: `{tx_result['signature']}`")
                st.write(f"Status: {tx_result.get('confirmation', {}).get('status', 'Unknown')}")
                st.write(f"Slot: {tx_result.get('confirmation', {}).get('slot', 'N/A')}")
                st.write(f"Execution Time: {tx_result['execution_time']:.3f}s")
                st.write(f"Confirmation Time: {tx_result.get('confirmation', {}).get('confirmation_time', 0):.3f}s")
            
            with col2:
                st.write("**Trade Details:**")
                st.write(f"Action: {signal['action']}")
                st.write(f"Size: {signal['size']:.4f} SOL (${trade['position_data']['position_size_usd']:.2f})")
                st.write(f"Price: ${signal['price']:.2f}")
                st.write(f"Confidence: {signal['confidence']:.1%}")
                st.write(f"Strategy: {signal['strategy']}")

def main():
    """Main dashboard function."""
    st.title("🚀 Enhanced Live Trading Dashboard")
    st.markdown("**Real-time monitoring of the Enhanced Trading System**")
    
    # Auto-refresh
    if st.sidebar.button("🔄 Refresh Data"):
        st.rerun()
    
    # Auto-refresh every 30 seconds
    if st.sidebar.checkbox("Auto-refresh (30s)", value=True):
        time.sleep(30)
        st.rerun()
    
    # Load data
    with st.spinner("Loading live trading data..."):
        metrics = load_enhanced_metrics()
        trades = load_enhanced_trades()
        cycles = load_enhanced_cycles()
    
    # Display system status
    display_system_status(metrics)
    
    st.markdown("---")
    
    # Display trading metrics
    display_trading_metrics(trades)
    
    st.markdown("---")
    
    # Display recent trades
    display_recent_trades(trades)
    
    st.markdown("---")
    
    # Display performance charts
    display_performance_charts(trades)
    
    st.markdown("---")
    
    # Display transaction details
    display_transaction_details(trades)
    
    # Sidebar info
    st.sidebar.markdown("### 📊 System Info")
    if metrics:
        st.sidebar.json({
            "Session Start": metrics['session_start'],
            "Duration (min)": f"{metrics['session_duration_minutes']:.1f}",
            "Cycles": metrics['metrics']['cycles_completed'],
            "Trades": len(trades),
            "Real Transactions": len([t for t in trades if t.get('transaction_result', {}).get('signature')])
        })
    
    # Footer
    st.markdown("---")
    st.markdown("**Enhanced Trading System** - Live trading with real transactions 🚀")
    st.markdown(f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
