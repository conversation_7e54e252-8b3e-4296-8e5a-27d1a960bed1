#!/usr/bin/env python3
"""
Create Keypair from .env Private Key
Securely converts Base58 private key to JSON keypair format for Synergy7 system.
"""

import os
import json
import base58
from dotenv import load_dotenv
from solders.keypair import Keypair
from pathlib import Path

def create_keypair_from_env():
    """Create keypair JSON file from .env private key."""
    
    # Load environment variables
    load_dotenv()
    
    # Get configuration from .env
    wallet_address = os.getenv('WALLET_ADDRESS')
    private_key_base58 = os.getenv('WALLET_PRIVATE_KEY')
    keypair_path = os.getenv('KEYPAIR_PATH', 'wallet/trading_wallet_keypair.json')
    
    print("🔐 Creating Keypair from .env Configuration")
    print("=" * 50)
    
    # Validate inputs
    if not wallet_address:
        print("❌ WALLET_ADDRESS not found in .env file")
        return False
    
    if not private_key_base58:
        print("❌ WALLET_PRIVATE_KEY not found in .env file")
        return False
    
    if private_key_base58 == "***SECURELY_STORED***":
        print("❌ Please replace WALLET_PRIVATE_KEY with your actual Base58 private key")
        return False
    
    print(f"📍 Wallet Address: {wallet_address}")
    print(f"📁 Keypair Path: {keypair_path}")
    print(f"🔑 Private Key: {private_key_base58[:8]}...{private_key_base58[-8:]} (truncated for security)")
    print()
    
    try:
        # Decode Base58 private key
        print("🔄 Decoding Base58 private key...")
        private_key_bytes = base58.b58decode(private_key_base58)
        
        # Create keypair from private key
        print("🔄 Creating keypair...")
        keypair = Keypair.from_bytes(private_key_bytes)
        derived_pubkey = str(keypair.pubkey())
        
        # Verify the public key matches the configured wallet address
        print("🔄 Verifying public key...")
        if derived_pubkey != wallet_address:
            print(f"❌ ERROR: Public key mismatch!")
            print(f"   Derived from private key: {derived_pubkey}")
            print(f"   Configured in .env:       {wallet_address}")
            print("   Please check your private key and wallet address.")
            return False
        
        print(f"✅ Public key verification successful: {derived_pubkey}")
        
        # Convert keypair to JSON array format
        print("🔄 Converting to JSON format...")
        keypair_bytes = bytes(keypair)
        keypair_array = list(keypair_bytes)
        
        # Ensure output directory exists
        output_path = Path(keypair_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save keypair to file
        print(f"🔄 Saving keypair to {keypair_path}...")
        with open(keypair_path, 'w') as f:
            json.dump(keypair_array, f)
        
        # Set secure file permissions (owner read/write only)
        os.chmod(keypair_path, 0o600)
        
        # Verify the saved keypair
        print("🔄 Verifying saved keypair...")
        with open(keypair_path, 'r') as f:
            saved_keypair_data = json.load(f)
        
        # Test loading the saved keypair
        test_keypair = Keypair.from_bytes(bytes(saved_keypair_data))
        test_pubkey = str(test_keypair.pubkey())
        
        if test_pubkey != wallet_address:
            print("❌ ERROR: Saved keypair verification failed!")
            return False
        
        print("✅ Saved keypair verification successful!")
        print()
        
        # Security check on file permissions
        file_stat = os.stat(keypair_path)
        file_perms = oct(file_stat.st_mode)[-3:]
        
        print("🛡️ Security Status:")
        print(f"   File permissions: {file_perms} {'✅ SECURE' if file_perms == '600' else '⚠️ INSECURE'}")
        print(f"   File size: {len(saved_keypair_data)} bytes")
        print(f"   Keypair format: JSON array ✅")
        print()
        
        print("🎉 SUCCESS: Keypair created and saved securely!")
        print("=" * 50)
        print(f"📍 Wallet Address: {wallet_address}")
        print(f"📁 Keypair File: {keypair_path}")
        print(f"🔒 File Permissions: 600 (secure)")
        print(f"✅ Ready for Enhanced Trading System")
        print()
        
        # Clean up sensitive variables
        private_key_base58 = None
        private_key_bytes = None
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: Failed to create keypair: {e}")
        return False

def validate_existing_keypair():
    """Validate existing keypair file if it exists."""
    
    load_dotenv()
    wallet_address = os.getenv('WALLET_ADDRESS')
    keypair_path = os.getenv('KEYPAIR_PATH', 'wallet/trading_wallet_keypair.json')
    
    if not os.path.exists(keypair_path):
        print(f"ℹ️ Keypair file does not exist: {keypair_path}")
        return False
    
    try:
        print(f"🔄 Validating existing keypair: {keypair_path}")
        
        with open(keypair_path, 'r') as f:
            keypair_data = json.load(f)
        
        keypair = Keypair.from_bytes(bytes(keypair_data))
        pubkey = str(keypair.pubkey())
        
        if pubkey == wallet_address:
            print(f"✅ Existing keypair is valid and matches wallet address")
            return True
        else:
            print(f"❌ Existing keypair does not match wallet address")
            print(f"   Keypair pubkey: {pubkey}")
            print(f"   Wallet address: {wallet_address}")
            return False
            
    except Exception as e:
        print(f"❌ Error validating existing keypair: {e}")
        return False

def main():
    """Main function."""
    print("🔐 Synergy7 Keypair Creation Tool")
    print("=" * 40)
    print()
    
    # Check if keypair already exists and is valid
    if validate_existing_keypair():
        print("✅ Valid keypair already exists!")
        overwrite = input("Do you want to recreate it from .env private key? (y/n): ").strip().lower()
        if overwrite != 'y':
            print("ℹ️ Keeping existing keypair.")
            return
        print()
    
    # Create keypair from .env
    success = create_keypair_from_env()
    
    if success:
        print("🚀 Next Steps:")
        print("1. Test the keypair: python3 scripts/test_transaction_fixes.py")
        print("2. Start live trading: python3 scripts/enhanced_live_trading.py --duration 0.5")
        print("3. Launch dashboard: streamlit run scripts/update_dashboard_for_production.py")
    else:
        print("❌ Keypair creation failed. Please check your .env configuration.")

if __name__ == "__main__":
    main()
