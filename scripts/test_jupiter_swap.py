#!/usr/bin/env python3
"""
Test Jupiter Swap Functionality

This script tests the Jupiter swap integration to ensure real swaps work correctly.
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from phase_4_deployment.rpc_execution.tx_builder import TxBuilder
from phase_4_deployment.rpc_execution.transaction_executor import TransactionExecutor
from phase_4_deployment.rpc_execution.helius_client import HeliusClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_jupiter_quote():
    """Test Jupiter quote functionality."""
    logger.info("🧪 Testing Jupiter Quote API...")

    # Load wallet and create transaction builder
    wallet_address = os.getenv('WALLET_ADDRESS', 'J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz')
    keypair_path = os.getenv('KEYPAIR_PATH', 'wallet/trading_wallet_keypair.json')

    try:
        # Load keypair
        from solders.keypair import Keypair
        import json

        with open(keypair_path, 'r') as f:
            keypair_data = json.load(f)
        keypair = Keypair.from_bytes(bytes(keypair_data))

        # Create transaction builder
        tx_builder = TxBuilder(wallet_address, keypair=keypair)

        # Test Jupiter quote
        SOL_MINT = "So11111111111111111111111111111111111111112"
        USDC_MINT = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"

        # Test small amount: 0.01 SOL
        amount = int(0.01 * 1_000_000_000)  # 0.01 SOL in lamports

        logger.info(f"Getting Jupiter quote for {amount} lamports SOL -> USDC")
        quote = await tx_builder._get_jupiter_quote(SOL_MINT, USDC_MINT, amount)

        if quote:
            logger.info("✅ Jupiter quote successful!")
            logger.info(f"   Input amount: {quote.get('inAmount', 'unknown')}")
            logger.info(f"   Output amount: {quote.get('outAmount', 'unknown')}")
            logger.info(f"   Price impact: {quote.get('priceImpactPct', 'unknown')}%")
            return True
        else:
            logger.error("❌ Jupiter quote failed")
            return False

    except Exception as e:
        logger.error(f"❌ Jupiter quote test error: {e}")
        return False
    finally:
        await tx_builder.close()

async def test_jupiter_swap_transaction():
    """Test Jupiter swap transaction building."""
    logger.info("🧪 Testing Jupiter Swap Transaction Building...")

    # Load wallet and create transaction builder
    wallet_address = os.getenv('WALLET_ADDRESS', 'J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz')
    keypair_path = os.getenv('KEYPAIR_PATH', 'wallet/trading_wallet_keypair.json')

    try:
        # Load keypair
        from solders.keypair import Keypair
        import json

        with open(keypair_path, 'r') as f:
            keypair_data = json.load(f)
        keypair = Keypair.from_bytes(bytes(keypair_data))

        # Create transaction builder
        tx_builder = TxBuilder(wallet_address, keypair=keypair)

        # Create a test signal for a small swap
        test_signal = {
            "action": "SELL",  # Sell SOL for USDC
            "market": "SOL-USDC",
            "price": 180.0,
            "size": 0.01,  # Very small amount for testing
            "confidence": 0.8,
            "timestamp": datetime.now().isoformat()
        }

        logger.info(f"Building Jupiter swap transaction for signal: {test_signal}")

        # Try to build Jupiter swap
        jupiter_tx = await tx_builder._build_real_jupiter_swap(test_signal)

        if jupiter_tx:
            logger.info("✅ Jupiter swap transaction built successfully!")
            logger.info(f"   Transaction type: {jupiter_tx.get('transaction_type')}")
            logger.info(f"   Skip simulation: {jupiter_tx.get('skip_simulation')}")
            logger.info(f"   Transaction size: {len(jupiter_tx.get('transaction', b''))} bytes")
            return True
        else:
            logger.warning("⚠️ Jupiter swap failed, this may be expected if we don't have USDC balance")
            return False

    except Exception as e:
        logger.error(f"❌ Jupiter swap transaction test error: {e}")
        return False
    finally:
        await tx_builder.close()

async def test_full_jupiter_integration():
    """Test full Jupiter integration with transaction executor."""
    logger.info("🧪 Testing Full Jupiter Integration...")

    # Load wallet and create components
    wallet_address = os.getenv('WALLET_ADDRESS', 'J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz')
    keypair_path = os.getenv('KEYPAIR_PATH', 'wallet/trading_wallet_keypair.json')

    try:
        # Load keypair
        from solders.keypair import Keypair
        import json

        with open(keypair_path, 'r') as f:
            keypair_data = json.load(f)
        keypair = Keypair.from_bytes(bytes(keypair_data))

        # Create transaction builder
        tx_builder = TxBuilder(wallet_address, keypair=keypair)

        # Create transaction executor
        helius_client = HeliusClient()
        executor = TransactionExecutor(helius_client)

        # Create a test signal for a small swap
        test_signal = {
            "action": "SELL",  # Sell SOL for USDC
            "market": "SOL-USDC",
            "price": 180.0,
            "size": 0.01,  # Very small amount for testing
            "confidence": 0.8,
            "timestamp": datetime.now().isoformat()
        }

        logger.info(f"Testing full Jupiter integration with signal: {test_signal}")

        # Build transaction (will try Jupiter first, fallback to simple test)
        tx_result = await tx_builder.build_swap_tx(test_signal)

        if tx_result:
            logger.info("✅ Transaction built successfully!")
            logger.info(f"   Transaction type: {tx_result.get('transaction_type', 'unknown')}")

            # Test transaction execution (dry run)
            logger.info("Testing transaction execution...")

            # Note: We won't actually execute to avoid spending real money
            # Just test that the executor can process the transaction format
            if isinstance(tx_result, dict) and 'transaction' in tx_result:
                logger.info("✅ Transaction format is compatible with executor")
                return True
            else:
                logger.warning("⚠️ Transaction format may not be compatible")
                return False
        else:
            logger.error("❌ Failed to build transaction")
            return False

    except Exception as e:
        logger.error(f"❌ Full Jupiter integration test error: {e}")
        return False
    finally:
        await tx_builder.close()
        await executor.close()

async def main():
    """Main test function."""
    logger.info("🚀 JUPITER SWAP INTEGRATION TEST")
    logger.info("=" * 60)

    tests = [
        ("Jupiter Quote API", test_jupiter_quote),
        ("Jupiter Swap Transaction", test_jupiter_swap_transaction),
        ("Full Jupiter Integration", test_full_jupiter_integration),
    ]

    results = []

    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        logger.info("-" * 40)

        try:
            result = await test_func()
            results.append((test_name, result))

            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.info(f"⚠️ {test_name}: FAILED (may be expected)")

        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))

    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 60)

    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "⚠️ FAILED"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1

    logger.info("-" * 60)
    logger.info(f"Total: {passed}/{len(results)} tests passed")

    if passed == len(results):
        logger.info("🎉 ALL TESTS PASSED - Jupiter integration is working!")
    elif passed > 0:
        logger.info("⚠️ PARTIAL SUCCESS - Some Jupiter features working")
    else:
        logger.info("❌ ALL TESTS FAILED - Jupiter integration needs work")

if __name__ == "__main__":
    asyncio.run(main())
