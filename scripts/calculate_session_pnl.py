#!/usr/bin/env python3
"""
Calculate PnL from Last Trading Session
Analyzes the last enhanced live trading session to calculate actual profit/loss.
"""

import json
import os
import glob
import asyncio
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.wallet.secure_wallet_manager import SecureWalletManager
from dotenv import load_dotenv

load_dotenv()

async def get_current_wallet_balance() -> float:
    """Get current wallet balance."""
    try:
        # Simple balance check using RPC
        import httpx
        
        wallet_address = os.getenv('WALLET_ADDRESS')
        rpc_url = "https://mainnet.helius-rpc.com/?api-key=dda9f776-9a40-447d-9ca4-22a27c21169e"
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                rpc_url,
                json={
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getBalance",
                    "params": [wallet_address]
                }
            )
            
            result = response.json()
            if 'result' in result and 'value' in result['result']:
                balance_lamports = result['result']['value']
                balance_sol = balance_lamports / 1_000_000_000
                return balance_sol
            else:
                print(f"Error getting balance: {result}")
                return 0.0
                
    except Exception as e:
        print(f"Error checking wallet balance: {e}")
        return 0.0

def load_session_trades() -> List[Dict[str, Any]]:
    """Load all trades from the last session."""
    trades = []
    
    trade_files = glob.glob("output/enhanced_live_trading/trades/trade_*.json")
    trade_files.sort()  # Chronological order
    
    for trade_file in trade_files:
        try:
            with open(trade_file, 'r') as f:
                trade_data = json.load(f)
                trades.append(trade_data)
        except Exception as e:
            print(f"Error loading {trade_file}: {e}")
    
    return trades

def load_session_metrics() -> Dict[str, Any]:
    """Load session metrics."""
    try:
        with open("output/enhanced_live_trading/latest_metrics.json", 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading metrics: {e}")
        return {}

async def calculate_session_pnl():
    """Calculate PnL from the last trading session."""
    
    print("💰 Calculating Last Trading Session PnL")
    print("=" * 45)
    
    # Load session data
    trades = load_session_trades()
    metrics = load_session_metrics()
    
    if not trades:
        print("❌ No trades found!")
        return
    
    print(f"📊 Session Overview:")
    print(f"   Start Time: {metrics.get('session_start', 'Unknown')}")
    print(f"   Duration: {metrics.get('session_duration_minutes', 0):.1f} minutes")
    print(f"   Total Trades: {len(trades)}")
    print(f"   Successful Trades: {len([t for t in trades if t.get('transaction_result', {}).get('success')])}")
    print()
    
    # Get wallet balances
    first_trade = trades[0]
    last_trade = trades[-1]
    
    session_start_balance = first_trade['position_data']['total_wallet_sol']
    session_end_balance = last_trade['position_data']['total_wallet_sol']
    
    print(f"💰 Wallet Balance Analysis:")
    print(f"   Session Start: {session_start_balance:.9f} SOL")
    print(f"   Session End: {session_end_balance:.9f} SOL")
    
    # Get current balance
    current_balance = await get_current_wallet_balance()
    print(f"   Current Balance: {current_balance:.9f} SOL")
    print()
    
    # Calculate PnL
    session_pnl_sol = session_end_balance - session_start_balance
    current_pnl_sol = current_balance - session_start_balance
    
    # Convert to USD (using average price from trades)
    prices = [t['signal']['price'] for t in trades if 'signal' in t and 'price' in t['signal']]
    avg_price = sum(prices) / len(prices) if prices else 180.0
    
    session_pnl_usd = session_pnl_sol * avg_price
    current_pnl_usd = current_pnl_sol * avg_price
    
    print(f"📈 Profit & Loss Analysis:")
    print(f"   Session PnL: {session_pnl_sol:+.9f} SOL (${session_pnl_usd:+.2f})")
    print(f"   Current PnL: {current_pnl_sol:+.9f} SOL (${current_pnl_usd:+.2f})")
    print(f"   Average SOL Price: ${avg_price:.2f}")
    print()
    
    # Trading activity analysis
    buy_trades = [t for t in trades if t.get('signal', {}).get('action') == 'BUY']
    sell_trades = [t for t in trades if t.get('signal', {}).get('action') == 'SELL']
    
    total_buy_volume = sum(t['position_data']['position_size_sol'] for t in buy_trades)
    total_sell_volume = sum(t['position_data']['position_size_sol'] for t in sell_trades)
    
    total_buy_value = sum(t['position_data']['position_size_usd'] for t in buy_trades)
    total_sell_value = sum(t['position_data']['position_size_usd'] for t in sell_trades)
    
    print(f"📊 Trading Activity:")
    print(f"   BUY Trades: {len(buy_trades)} ({total_buy_volume:.4f} SOL, ${total_buy_value:.2f})")
    print(f"   SELL Trades: {len(sell_trades)} ({total_sell_volume:.4f} SOL, ${total_sell_value:.2f})")
    print(f"   Net Volume: {total_buy_volume - total_sell_volume:+.4f} SOL")
    print(f"   Net Value: ${total_buy_value - total_sell_value:+.2f}")
    print()
    
    # Fee analysis (estimated)
    successful_trades = [t for t in trades if t.get('transaction_result', {}).get('success')]
    estimated_fees_sol = len(successful_trades) * 0.000005  # ~5000 lamports per transaction
    estimated_fees_usd = estimated_fees_sol * avg_price
    
    print(f"💸 Fee Analysis (Estimated):")
    print(f"   Transaction Fees: ~{estimated_fees_sol:.6f} SOL (~${estimated_fees_usd:.2f})")
    print(f"   Fee per Trade: ~{estimated_fees_sol/len(successful_trades):.6f} SOL" if successful_trades else "   No successful trades")
    print()
    
    # Performance metrics
    if session_pnl_sol != 0:
        roi_percent = (session_pnl_sol / session_start_balance) * 100
        print(f"📊 Performance Metrics:")
        print(f"   ROI: {roi_percent:+.4f}%")
        print(f"   Hourly Rate: ${session_pnl_usd / (metrics.get('session_duration_minutes', 1) / 60):+.2f}/hour")
        print()
    
    # Summary
    print("🎯 Session Summary:")
    if session_pnl_sol > 0:
        print(f"   ✅ PROFITABLE SESSION: +{session_pnl_sol:.6f} SOL (${session_pnl_usd:+.2f})")
    elif session_pnl_sol < 0:
        print(f"   ❌ LOSS SESSION: {session_pnl_sol:.6f} SOL (${session_pnl_usd:.2f})")
    else:
        print(f"   ➖ BREAK-EVEN SESSION: {session_pnl_sol:.6f} SOL")
    
    print(f"   💰 Current Status: {current_pnl_sol:+.6f} SOL (${current_pnl_usd:+.2f}) vs session start")
    
    # Real transaction verification
    real_transactions = [t for t in trades if t.get('transaction_result', {}).get('signature')]
    print(f"   ✅ Real Transactions: {len(real_transactions)}/{len(trades)} executed on blockchain")

def main():
    """Main function."""
    try:
        asyncio.run(calculate_session_pnl())
    except Exception as e:
        print(f"❌ Error calculating PnL: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
