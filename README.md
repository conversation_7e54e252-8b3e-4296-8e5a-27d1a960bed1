# Synergy7 Enhanced Trading System

A high-frequency trading system for Solana with a hybrid Rust/Python architecture and **production-ready transaction execution**.

## Overview

The Synergy7 Trading System is a high-performance trading platform designed for the Solana blockchain. It combines the speed and efficiency of Rust with the flexibility and ecosystem of Python to create a powerful hybrid architecture.

Key features:
- Hybrid Rust/Python architecture for optimal performance
- **Production-Ready Transaction System** with comprehensive error handling
- **Enhanced Transaction Execution** with multi-RPC failover and retry logic
- **Secure Wallet Management** with keypair validation and encryption support
- Stream data ingestion for real-time market data
- Advanced market microstructure analysis
- Statistical signal processing for trading signals
- Reinforcement learning for execution optimization
- **Enhanced 4-Phase Trading System** with real-time monitoring
- **Real-time Dashboard Suite** for comprehensive monitoring
- **Enhanced Paper Trading Monitor** with live strategy visualization
- Support for multiple RPC providers (Helius, Jito, Lil' Jito)
- Enhanced risk management with circuit breakers and dynamic position sizing
- Optimized momentum strategy with walk-forward optimization
- API resilience with circuit breakers and rate limiting
- **Jupiter DEX Integration** with intelligent fallback mechanisms

## Architecture

The system follows a hybrid architecture with the following components:

### Enhanced Transaction System (NEW)
- **Enhanced Transaction Builder**: Complete Jupiter DEX integration with fallback mechanisms
- **Enhanced Transaction Executor**: Multi-RPC execution with intelligent retry logic
- **Secure Wallet Manager**: Comprehensive keypair validation and secure management
- **Production Position Sizer**: Optimized position sizing for 0.5 wallet strategy

### Rust Components
- **Carbon Core**: High-performance data processing engine
- **Transaction Preparation Service**: Builds, signs, and serializes transactions
- **Wallet Manager**: Secure wallet handling
- **Communication Layer**: ZeroMQ-based communication between Rust and Python

### Python Components
- **Orchestration Layer**: Coordinates the overall system flow
- **Strategy Runner**: Executes trading strategies
- **Strategy Optimizer**: Optimizes strategy parameters using walk-forward analysis
- **Enhanced 4-Phase Trading System**:
  - **Phase 1**: Enhanced Market Regime Detection & Whale Watching
  - **Phase 2**: Advanced Risk Management (VaR/CVaR)
  - **Phase 3**: Strategy Performance Attribution
  - **Phase 4**: Adaptive Strategy Weighting
- **Risk Management**:
  - **Position Sizer**: Dynamic position sizing based on volatility
  - **Stop Loss Manager**: Trailing stops and time-based widening
  - **Portfolio Limits**: Exposure and drawdown controls
  - **Circuit Breaker**: Automatic trading halt on risk threshold breaches
- **RPC Clients**: Communicates with Solana RPC providers with circuit breakers and rate limiting
- **Real-time Monitoring Suite**:
  - **Enhanced Trading Dashboard**: Live strategy and performance monitoring
  - **System Monitoring Dashboard**: Health and API status monitoring
  - **Paper Trading Monitor**: Real-time strategy testing and validation

## Directory Structure

```
📦 Synergy7_System/
├── config.yaml                      # Consolidated configuration
├── config_example.yaml              # Template with documentation
├── .env                             # Environment variables and API keys
├── simple_paper_trading_monitor.py  # Enhanced paper trading monitor
├── enhanced_trading_dashboard.py    # Real-time trading dashboard
├── simple_monitoring_dashboard.py   # System health monitoring dashboard
├── phase_4_deployment/
│   ├── start_live_trading.py        # Main Python runner
│   ├── unified_runner.py            # Unified entry point
│   ├── docker_deploy/
│   │   └── entrypoint.sh            # Primary entry point
│   ├── data_router/
│   │   ├── birdeye_scanner.py       # Supplemental data source
│   │   └── whale_watcher.py         # Supplemental data source
│   ├── rpc_execution/
│   │   ├── transaction_executor.py  # Unified transaction executor
│   │   ├── helius_client.py         # Helius RPC client with circuit breaker
│   │   ├── jito_client.py           # Jito RPC client with circuit breaker
│   │   ├── lil_jito_client.py       # Lil' Jito RPC client
│   │   └── tx_builder.py            # Transaction builder
│   ├── core/
│   │   ├── risk_manager.py          # Risk management
│   │   ├── portfolio_risk.py        # Portfolio-level risk
│   │   ├── position_sizer.py        # Position sizing
│   │   ├── signal_enricher.py       # Signal enrichment
│   │   ├── tx_monitor.py            # Transaction monitoring
│   │   ├── wallet_state.py          # Wallet state management
│   │   └── shutdown_handler.py      # Graceful shutdown
│   ├── monitoring/
│   │   ├── telegram_alerts.py       # Alert notifications
│   │   └── health_check.py          # System health checks
│   ├── unified_dashboard/           # Unified dashboard
│   │   ├── app.py                   # Main dashboard application
│   │   ├── data_service.py          # Centralized data service
│   │   ├── run_dashboard.py         # Dashboard runner
│   │   └── components/              # Dashboard components
│   ├── stream_data_ingestor/
│   │   └── client.py                # Stream data client
│   └── python_comm_layer/
│       └── client.py                # Python-Rust communication
├── core/
│   ├── strategies/
│   │   ├── momentum_optimizer.py           # Momentum strategy optimizer
│   │   ├── market_regime_detector.py       # Enhanced market regime detection
│   │   ├── probabilistic_regime.py         # Probabilistic regime detection
│   │   ├── adaptive_weight_manager.py      # Adaptive strategy weighting
│   │   ├── strategy_selector.py            # Intelligent strategy selection
│   │   └── README.md                       # Strategy documentation
│   ├── risk/
│   │   ├── position_sizer.py               # Dynamic position sizing
│   │   ├── stop_loss.py                    # Stop loss management
│   │   ├── portfolio_limits.py             # Portfolio-level risk controls
│   │   ├── circuit_breaker.py              # Trading circuit breaker
│   │   ├── var_calculator.py               # VaR/CVaR risk calculations
│   │   ├── portfolio_risk_manager.py       # Portfolio risk management
│   │   └── README.md                       # Risk management documentation
│   ├── data/
│   │   └── whale_signal_generator.py       # Whale transaction monitoring
│   ├── signals/
│   │   └── whale_signal_processor.py       # Whale signal processing
│   ├── analytics/
│   │   ├── strategy_attribution.py         # Strategy performance attribution
│   │   └── performance_analyzer.py         # Performance analysis
│   └── monitoring/
│       └── system_metrics.py               # System health monitoring
├── shared/
│   └── utils/
│       └── config_loader.py         # Centralized configuration loader
├── rust_tx_prep_service/
│   └── src/
│       ├── lib.rs                   # Rust service entry point
│       ├── transaction_builder.rs   # Transaction building
│       └── signer.rs                # Transaction signing
├── rust_wallet_manager/
│   └── src/
│       └── lib.rs                   # Secure wallet handling
├── rust_comm_layer/
│   └── src/
│       └── lib.rs                   # Rust-Python communication
├── carbon_core/
│   └── src/
│       ├── lib.rs                   # Carbon Core base
│       ├── account.rs               # Account processing
│       ├── processor.rs             # Signal processing trait
│       └── transformers.rs          # Data transformation
└── solana_tx_utils/                 # PyO3 extension
    └── src/
        └── lib.rs                   # PyO3 bindings
```

## Installation

### Prerequisites
- Python 3.9 or higher
- Rust 1.60 or higher
- ZeroMQ library

### Installation Steps

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/synergy7_system.git
   cd synergy7_system
   ```

2. Run the installation script:
   ```bash
   chmod +x install_requirements.sh
   ./install_requirements.sh
   ```

3. Configure the system:
   - Copy `config_example.yaml` to `config.yaml` and edit as needed
   - Create a `.env` file with your API keys and secrets

## Configuration

The system uses a consolidated `config.yaml` file for all configuration settings. The file is organized into the following sections:

- `mode`: Operational mode (live, paper, backtest, simulation)
- `solana`: Solana RPC configuration
- `wallet`: Wallet configuration
- `strategies`: Trading strategy configuration
- `risk_management`:
  - `position_sizer`: Dynamic position sizing parameters
  - `stop_loss`: Stop loss management parameters
  - `portfolio_limits`: Portfolio-level risk controls
  - `circuit_breaker`: Trading circuit breaker parameters
- `execution`: Transaction execution parameters
- `monitoring`: Monitoring and alerting configuration
- `apis`:
  - `helius`: Helius API configuration with circuit breaker parameters
  - `jito`: Jito API configuration with circuit breaker parameters
  - `birdeye`: Birdeye API configuration
- `logging`: Logging configuration
- `carbon_core`: Rust Carbon Core configuration

## Usage

### Running the Enhanced Trading System

#### Enhanced Live Trading (Recommended)
Production-ready live trading with comprehensive error handling:

```bash
# Test transaction system first
python3 scripts/test_transaction_fixes.py

# Start enhanced live trading (30 minutes)
python3 scripts/enhanced_live_trading.py --duration 0.5

# Extended session (2 hours)
python3 scripts/enhanced_live_trading.py --duration 2.0

# Full day session (24 hours)
python3 scripts/enhanced_live_trading.py --duration 24.0
```

#### Enhanced Dashboard Suite
Real-time monitoring with multiple specialized dashboards:

```bash
# Production dashboard (recommended)
streamlit run scripts/update_dashboard_for_production.py

# Main trading dashboard
streamlit run dashboard/main_dashboard.py

# Performance analytics dashboard
streamlit run dashboard/performance_dashboard.py
```

#### Legacy System Modes
The system can also be run in different modes using the primary entry point:

- **Live Trading**: Real trading with real funds
  ```bash
  python run_synergy7.py --mode live
  ```

- **Paper Trading**: Simulated trading with real market data
  ```bash
  python run_synergy7.py --mode paper
  ```

- **Backtesting**: Testing strategies on historical data
  ```bash
  python run_synergy7.py --mode backtest
  ```

- **Simulation**: End-to-end simulation with mock data
  ```bash
  python run_synergy7.py --mode simulation
  ```

> **Note**: The `run_synergy7.py` script is the recommended entry point for all production deployments. It provides a standardized interface to the Synergy7 Trading System and handles all the necessary initialization and configuration.

For more information about the system's entry points, see the [ENTRY_POINTS.md](ENTRY_POINTS.md) file.

### Enhanced Paper Trading with Real-time Monitoring

The Enhanced Paper Trading Monitor provides comprehensive strategy testing with real-time visualization:

```bash
# Run enhanced paper trading monitor (2-minute cycles for 20 minutes)
python simple_paper_trading_monitor.py --interval 2 --duration 20

# Run continuously (Ctrl+C to stop)
python simple_paper_trading_monitor.py --interval 3
```

### Real-time Dashboard Suite

The system includes multiple specialized dashboards for comprehensive monitoring:

#### Enhanced Trading Dashboard
Real-time trading strategy monitoring and performance visualization:

```bash
# Start enhanced trading dashboard
streamlit run enhanced_trading_dashboard.py --server.port 8504
```

**Features**:
- Live 4-phase trading system monitoring
- Real-time market regime detection
- Strategy performance attribution
- Adaptive weight management visualization
- Risk metrics (VaR/CVaR) tracking
- Historical performance charts

**Access**: http://localhost:8504

#### System Monitoring Dashboard
System health and API status monitoring:

```bash
# Start system monitoring dashboard
streamlit run simple_monitoring_dashboard.py --server.port 8503
```

**Features**:
- Real-time system resource monitoring (CPU, Memory, Disk)
- API connectivity status (Helius, Birdeye)
- Log analysis and error tracking
- System health indicators

**Access**: http://localhost:8503

#### Unified Dashboard (Legacy)
Comprehensive system monitoring and analytics:

```bash
# Using the Python script
python phase_4_deployment/unified_dashboard/run_dashboard.py

# Using the shell script
./phase_4_deployment/run_unified_dashboard.sh
```

**Features**:
- System overview and health monitoring
- Trading metrics and performance analysis
- Market data and opportunities
- Advanced trading models visualization
- System resource monitoring

**Access**: http://localhost:8502

### Docker Deployment

The system can be deployed using Docker:

```bash
docker-compose up -d
```

## Development

### Building Rust Components

To build the Rust components:

```bash
cd carbon_core
cargo build --release
cd ..

cd rust_tx_prep_service
cargo build --release
cd ..

cd rust_comm_layer
cargo build --release
cd ..

cd solana_tx_utils
maturin develop
cd ..
```

### Running Tests

To run all tests:

```bash
python tests/run_tests.py
```

To run specific test modules:

```bash
# Run risk management tests
python -m unittest tests.test_position_sizer tests.test_stop_loss tests.test_portfolio_limits tests.test_circuit_breaker

# Run strategy optimizer tests
python -m unittest tests.test_momentum_optimizer

# Run system test
python scripts/system_test.py
```

To purge mean reversion strategy files:

```bash
# Dry run (shows files that would be removed)
python scripts/purge_mean_reversion.py

# Actually remove files
python scripts/purge_mean_reversion.py --remove
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
